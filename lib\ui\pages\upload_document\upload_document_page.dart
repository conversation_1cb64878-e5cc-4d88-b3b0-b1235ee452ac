import 'dart:io';

import 'package:el_tooltip/el_tooltip.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';

import '../../../base/base_page_state.dart';
import 'cubit/upload_document_state.dart';

class UploadDocumentPage extends StatefulWidget {
  static const routeName = 'UploadDocumentPage';

  final bool isFromWelcome;
  final SharedMediaFile? sharedMediaFile;
  final VoidCallback? onClose;
  final VoidCallback? onCreateNoteSuccess;

  const UploadDocumentPage({
    super.key,
    this.sharedMediaFile,
    this.isFromWelcome = false,
    this.onClose,
    this.onCreateNoteSuccess,
  });

  @override
  State<UploadDocumentPage> createState() => _UploadAudioPageState();
}

class _UploadAudioPageState
    extends BasePageStateDelegate<UploadDocumentPage, UploadDocumentCubit> {
  @override
  void initState() {
    super.initState();
    if (widget.sharedMediaFile != null) {
      _handleShareMediaFile(widget.sharedMediaFile!);
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          _onSelectDocumentFile();
        }
      });
    }

    cubit.logEventTrackingOpenDocPage();
  }

  @override
  void didUpdateWidget(UploadDocumentPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.sharedMediaFile != null) {
      _handleShareMediaFile(widget.sharedMediaFile!);
    }
    widget.onClose?.call();
  }

  @override
  void dispose() {
    super.dispose();
    widget.onClose?.call();
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<UploadDocumentCubit, UploadDocumentState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent &&
          current.oneShotEvent != CreateNoteWithDocumentOneShotEvent.none,
      listener: (context, state) async {
        switch (state.oneShotEvent) {
          case CreateNoteWithDocumentOneShotEvent.none:
            break;
          case CreateNoteWithDocumentOneShotEvent.createNoteSuccessfully:
            // Save note model before reset state
            final noteModel = cubit.getDocumentNote();

            // Reset state before navigating
            cubit.resetState();

            // Call the onClose and onCreateNoteSuccess callbacks
            widget.onClose?.call();
            widget.onCreateNoteSuccess?.call();

            // Navigate
            Navigator.of(context).pop();
            final savedTabs =
                await GetIt.instance.get<LocalService>().loadSelectedItems();
            await Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => MyNoteDetailPage(
                  noteModel: noteModel,
                  isTablet: cubit.appCubit.isTablet,
                  from: NoteDetailPageFrom.documentScreen,
                  savedTabs: savedTabs,
                ),
              ),
            );
            break;
          case CreateNoteWithDocumentOneShotEvent.invalidFileType:
            showBlackCupertinoDialog(
              context: context,
              title: '',
              message: S.current.invalid_file_type,
              confirmButton: S.current.ok,
              onConfirm: () {},
              confirmButtonTextColor: context.colorScheme.mainBlue,
            );
            break;
          case CreateNoteWithDocumentOneShotEvent.onShowIAPFromDocument:
            Navigator.of(context).pushNamed(
              PurchasePage.routeName,
              arguments: {
                EventKey.from: PurchasePageFrom.iapCreateNote,
              },
            ).then((didPurchaseSuccess) => {
                  if (didPurchaseSuccess == true)
                    {cubit.onSubmitUploadDocument()}
                });
            break;
        }
        cubit.resetEnumState();
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
        appBar: AppBarWidget(
          isShowLeftButton: true,
          title: S.current.document_tab,
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 16.h,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: context.isLandscape
                            ? 75
                            : cubit.appCubit.isTablet
                                ? 56
                                : 56.h,
                        padding: cubit.appCubit.isTablet
                            ? const EdgeInsets.symmetric(
                                horizontal: 11,
                                vertical: 8,
                              )
                            : EdgeInsets.symmetric(
                                horizontal: 11.w,
                                vertical: 8.h,
                              ),
                        width: double.infinity,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: AppColors.gradientCTABlue,
                            begin: Alignment.centerRight,
                            end: Alignment.centerLeft,
                          ),
                          borderRadius: cubit.appCubit.isTablet
                              ? BorderRadius.circular(54)
                              : BorderRadius.circular(54.r),
                        ),
                        child: BlocBuilder<UploadDocumentCubit,
                            UploadDocumentState>(
                          buildWhen: (previous, current) =>
                              previous.documentFilePath !=
                              current.documentFilePath,
                          builder: (context, state) => state
                                  .documentFilePath.isEmpty
                              ? InkWell(
                                  onTap: () => _onSelectDocumentFile(),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        Assets.icons.icUploadFile,
                                        colorFilter: const ColorFilter.mode(
                                          AppColors.white,
                                          BlendMode.srcIn,
                                        ),
                                        width:
                                            cubit.appCubit.isTablet ? 24 : 24.w,
                                        height:
                                            cubit.appCubit.isTablet ? 24 : 24.w,
                                      ),
                                      AppConstants.kSpacingItemW4,
                                      Padding(
                                        padding:
                                            EdgeInsets.symmetric(vertical: 4.w),
                                        child: Text(
                                          S.current.upload_file,
                                          style: TextStyle(
                                            fontSize: cubit.appCubit.isTablet
                                                ? 18
                                                : 16.sp,
                                            color: AppColors.white,
                                            fontWeight: FontWeight.w600,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : Row(
                                  children: [
                                    Expanded(
                                      child: CommonText(
                                        state.documentFileName,
                                        style: TextStyle(
                                          fontSize: cubit.appCubit.isTablet
                                              ? 16
                                              : 14.sp,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        textColor:
                                            context.colorScheme.themeWhite,
                                        maxLines: 1,
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    GestureDetector(
                                        onTap: () => cubit.clearFile(false),
                                        child: SvgPicture.asset(
                                          Assets.icons.icCloseWhite,
                                          width: cubit.appCubit.isTablet
                                              ? 24
                                              : 24.w,
                                          height: cubit.appCubit.isTablet
                                              ? 24
                                              : 24.h,
                                        )),
                                  ],
                                ),
                        ),
                      ),
                      AppConstants.kSpacingItem4,
                      BlocBuilder<UploadDocumentCubit, UploadDocumentState>(
                        buildWhen: (previous, current) =>
                            previous.isDocumentFileOverLimit !=
                            current.isDocumentFileOverLimit,
                        builder: (context, state) =>
                            state.isDocumentFileOverLimit
                                ? Center(
                                    child: CommonText(
                                      S.current.document_exceed_limit,
                                      textColor: AppColors.primaryRed,
                                      style: TextStyle(
                                        fontSize: cubit.appCubit.isTablet
                                            ? 15
                                            : 12.sp,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                      ),
                      Center(
                        child: CommonText(
                          S.current.document_type,
                          textColor: context.colorScheme.mainGray,
                          style: TextStyle(
                            fontSize: cubit.appCubit.isTablet ? 14 : 12.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                      AppConstants.kSpacingItem16,
                      CommonText(
                        S.current.folder,
                        textColor: context.colorScheme.mainGray,
                        style: TextStyle(
                          fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      AppConstants.kSpacingItem8,
                      SizedBox(
                        width: double.infinity,
                        child: FolderDropdownView(
                          folders: [
                            FolderModel(
                                id: 'all_notes',
                                folderName: S.current.all_note,
                                backendId: ''),
                            ...HiveFolderService.getAllFolders()
                          ],
                          selectedFolder: cubit.tabDocumentChangeFolderNotifier,
                          onMenuStateChanged: (isShowing) {},
                          from: FolderDropdownFrom.document,
                        ),
                      ),
                      AppConstants.kSpacingItem16,
                      Row(
                        children: [
                          CommonText(
                            S.current.summary_language,
                            textColor: context.colorScheme.mainGray,
                            style: TextStyle(
                              fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          AppConstants.kSpacingItemW4,
                          ElTooltip(
                            padding: EdgeInsets.all(8.w),
                            content: const LanguageTipsWidget(
                              isRecording: false,
                              isCheckDocAndText: true,
                            ),
                            position: ElTooltipPosition.bottomCenter,
                            color: context.colorScheme.mainBlue,
                            radius: Radius.circular(16.r),
                            child: SvgPicture.asset(
                              Assets.icons.icCommonInfoTooltip,
                              height: cubit.appCubit.isTablet ? 18 : 18.w,
                              width: cubit.appCubit.isTablet ? 18 : 18.w,
                              colorFilter: ColorFilter.mode(
                                context.colorScheme.mainPrimary,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ],
                      ),
                      AppConstants.kSpacingItem8,
                      SizedBox(
                        width: double.infinity,
                        child: LanguageDropdownView(
                          initialLanguageCode:
                              cubit.targetAudioFileLanguage?.code,
                          onChanged: (lang) =>
                              {cubit.setDocumentLanguage(lang)},
                          onMenuStateChanged: (isShowing) {
                            if (isShowing) {}
                          },
                          useCase:
                              LanguageDropdownUseCase.translateLanguageWithAuto,
                          from: LanguageDropdownFrom.document,
                        ),
                      ),
                      AppConstants.kSpacingItem16,
                      AdvancedWidget(
                        onSummaryStyleChanged: cubit.updateSummaryStyle,
                        onWritingStyleChanged: cubit.updateWritingStyle,
                        onAdditionalInstructionsChanged:
                            cubit.updateAdditionalInstructions,
                        onAdvancedToggled: cubit.setAdvancedEnabled,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  top: 16,
                  bottom: MediaQuery.of(context).viewInsets.bottom > 0
                      ? 16.h
                      : context.isTablet
                          ? 32
                          : 32.h),
              child: BlocBuilder<UploadDocumentCubit, UploadDocumentState>(
                buildWhen: (previous, current) =>
                    previous.documentFilePath != current.documentFilePath,
                builder: (context, state) {
                  return _buildButtonSubmit(
                    isEnable: state.documentFilePath.isNotEmpty,
                    createNoteType: NoteType.document,
                  );
                },
              ),
            )
          ],
        ));
  }

  Future<void> _onSelectDocumentFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: Platform.isAndroid ? FileType.any : FileType.custom,
          allowedExtensions:
              Platform.isAndroid ? null : cubit.validDocumentExtensions);
      if (result != null) {
        File file = File(result.files.single.path ?? '');
        String name = result.files.single.name;

        if (!cubit.isDocumentFile(file.path)) {
          cubit.showInvalidFileTypeDialog();
          return;
        }

        final isFileUnderLimit = await isFileSizeUnderLimit(file);
        cubit.setIsDocumentFileOverLimit(!isFileUnderLimit);
        isFileUnderLimit ? cubit.onDocumentFilePicked(file.path, name) : ();
      }
    } catch (e) {
      debugPrint('Error picking file: $e');
    }
  }

  Widget _buildButtonSubmit({
    bool isEnable = true,
    required NoteType createNoteType,
  }) {
    return Center(
      child: AppCommonButton(
        width: cubit.appCubit.isTablet ? 200 : 160.w,
        height: cubit.appCubit.isTablet ? 44 : 44.h,
        borderRadius: BorderRadius.circular(24.r),
        backgroundColor: isEnable
            ? context.colorScheme.mainBlue
            : context.colorScheme.mainNeutral,
        textWidget: Text(
          S.current.add_to_notes,
          style: TextStyle(
            fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
            fontWeight: FontWeight.w500,
            color: isEnable
                ? context.colorScheme.themeWhite
                : context.colorScheme.mainPrimary.withOpacity(0.38),
          ),
        ),
        onPressed: () async {
          if (isEnable) {
            cubit.focusNodeCreateNote.unfocus();
            cubit.onSubmitUploadDocument();
          }
        },
      ),
    );
  }

  void _handleShareMediaFile(SharedMediaFile sharedMediaFile) async {
    if (widget.sharedMediaFile != null) {
      final fileName = sharedMediaFile.path.split('/').last;
      final filePath = sharedMediaFile.path;
      cubit.clearFile(false);
      final isFileUnderLimit = await isFileSizeUnderLimit(File(filePath));
      cubit.setIsDocumentFileOverLimit(!isFileUnderLimit);
      isFileUnderLimit ? cubit.onDocumentFilePicked(filePath, fileName) : ();
    }
  }
}
