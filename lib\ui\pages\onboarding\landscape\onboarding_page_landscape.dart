import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../../../ui/pages/onboarding/cubit/onboarding_state.dart';

class OnboardingLandscapePage extends StatefulWidget {
  const OnboardingLandscapePage({super.key});

  @override
  State<OnboardingLandscapePage> createState() =>
      _OnboardingLandscapePageState();
}

class _OnboardingLandscapePageState
    extends BasePageStateDelegate<OnboardingLandscapePage, OnboardingCubit>
    with TickerProviderStateMixin {
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      AppsFlyerService.initATTAppsFlyersFacebookTiktok();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  List<OnboardingModel> get onboardingLandScapeItems =>
      OnboardingLandScapeItems.getDataStudentItem(context);

  List<OnboardingModel> get onboardingLandScapeProfessionalItems =>
      OnboardingLandScapeProfessionalItems.getDataProfessionalItem(context);

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<OnboardingCubit, OnboardingState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: context.colorScheme.mainBackground,
          body: Stack(
            children: [
              if (state.isGetStartedScreen) ...[
                Positioned(
                  child: Container(
                    decoration: context.isDarkMode
                        ? BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage(
                                Assets.images.bgrBlurOb1.path,
                              ),
                              fit: BoxFit.fill,
                            ),
                          )
                        : null,
                  ),
                ),
                Center(
                  child: Lottie.asset(
                    Assets.videos.onb1,
                    height: double.infinity,
                  ),
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        Assets.icons.icAverageRating,
                        width: 166.w,
                        height: 65.h,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                      AppConstants.kSpacingItem16,
                      _buildContentIntroStarted(),
                      AppConstants.kSpacingItem20,
                      _buildGetStartedButton(),
                      AppConstants.kSpacingItem30,
                    ],
                  ),
                ),
              ] else if (state.isChooseTypeScreen) ...[
                _showChooseTypePage(state)
              ] else if (state.isCustomTabScreen) ...[
                const CustomTabPage()
              ] else ...[
                if (state.selectedTypeIndex == 0) ...[
                  PageView.builder(
                    controller: _pageController,
                    itemCount: onboardingLandScapeItems.length,
                    itemBuilder: (context, index) {
                      final items = onboardingLandScapeItems[index];
                      return Stack(
                        children: [
                          Center(child: _buildBackgroundImages(index)),

                          /// blur
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: Container(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(
                                    Assets.images.bgBlurOb.path,
                                  ),
                                  fit: BoxFit.fill,
                                ),
                              ),
                              height: MediaQuery.of(context).size.height * 0.46,
                            ),
                          ),

                          /// Content of PageView
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: MediaQuery.of(context).size.height * 0.2,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                _buildContentIntroTablet(items),
                                AppConstants.kSpacingItem8,
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                    onPageChanged: (value) {
                      cubit.trackingEventTypeStudent(value - 1);
                      cubit.updateCurrentIndex(value);
                    },
                  ),

                  /// Dot Indicator
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Column(
                      children: [
                        Center(
                          child: SmoothPageIndicator(
                            controller: _pageController,
                            count: onboardingLandScapeItems.length,
                            effect: JumpingDotEffect(
                              dotWidth: cubit.appCubit.isTablet ? 12 : 8,
                              dotHeight: cubit.appCubit.isTablet ? 12 : 8,
                              activeDotColor: context.colorScheme.mainBlue,
                              dotColor:
                                  context.colorScheme.mainGray.withOpacity(0.5),
                              spacing: cubit.appCubit.isTablet ? 16 : 12,
                              verticalOffset: 10,
                              jumpScale: 2.0,
                            ),
                          ),
                        ),
                        AppConstants.kSpacingItem32,
                        _buildContinueWidget(),
                        AppConstants.kSpacingItem30,
                      ],
                    ),
                  ),
                ] else ...[
                  PageView.builder(
                    controller: _pageController,
                    itemCount: onboardingLandScapeProfessionalItems.length,
                    itemBuilder: (context, index) {
                      final items = onboardingLandScapeProfessionalItems[index];
                      return Stack(
                        children: [
                          Center(
                            child: _buildBackgroundImagesProfessional(index),
                          ),

                          /// blur
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: Container(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(
                                    Assets.images.bgBlurOb.path,
                                  ),
                                  fit: BoxFit.fill,
                                ),
                              ),
                              height: MediaQuery.of(context).size.height * 0.46,
                            ),
                          ),

                          /// Content of PageView
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: MediaQuery.of(context).size.height * 0.2,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                _buildContentIntroTablet(items),
                                AppConstants.kSpacingItem8,
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                    onPageChanged: (value) {
                      cubit.trackingEventTypeProfessional(value - 1);
                      cubit.updateCurrentIndex(value);
                    },
                  ),

                  /// Dot Indicator
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Column(
                      children: [
                        Center(
                          child: SmoothPageIndicator(
                            controller: _pageController,
                            count: onboardingLandScapeProfessionalItems.length,
                            effect: JumpingDotEffect(
                              dotWidth: cubit.appCubit.isTablet ? 12 : 8,
                              dotHeight: cubit.appCubit.isTablet ? 12 : 8,
                              activeDotColor: context.colorScheme.mainBlue,
                              dotColor:
                                  context.colorScheme.mainGray.withOpacity(0.5),
                              spacing: cubit.appCubit.isTablet ? 16 : 12,
                              verticalOffset: 10,
                              jumpScale: 2.0,
                            ),
                          ),
                        ),
                        AppConstants.kSpacingItem32,
                        _buildContinueWidget(),
                        AppConstants.kSpacingItem30,
                      ],
                    ),
                  ),
                ],
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildContentIntroTablet(OnboardingModel item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Center(
          child: CommonText(
            item.content,
            style: const TextStyle(fontSize: 36),
            textColor: Colors.white,
            appFontWeight: AppFontWeight.semiBold,
            letterSpacing: 0.48,
          ),
        ),
        AppConstants.kSpacingItem8,
        Center(
          child: CommonText(
            item.title,
            style: const TextStyle(fontSize: 22),
            textColor: context.colorScheme.themeWhite,
            appFontWeight: AppFontWeight.regular,
            letterSpacing: 0.48,
          ),
        ),
      ],
    );
  }

  Widget dotIndicator({required bool isSelected}) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: AnimatedContainer(
        duration: const Duration(microseconds: 500),
        height: isSelected ? 12 : 8,
        width: isSelected ? 12 : 8,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isSelected ? Colors.white : context.colorScheme.mainNeutral,
        ),
      ),
    );
  }

  Widget _buildContinueWidget() {
    return BlocBuilder<OnboardingCubit, OnboardingState>(
      builder: (context, state) {
        final isLastItem = state.selectedTypeIndex == 0
            ? state.currentIndex == onboardingLandScapeItems.length - 1
            : state.currentIndex ==
                onboardingLandScapeProfessionalItems.length - 1;

        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: cubit.appCubit.isTablet ? 180 : 48.w,
          ),
          child: AppCommonButton(
            width: MediaQuery.of(context).size.height * 0.4,
            height: 56,
            borderRadius: BorderRadius.circular(100.r),
            gradient: const LinearGradient(
              begin: Alignment.centerRight,
              end: Alignment.centerLeft,
              colors: AppColors.gradientCTABlue,
            ),
            textWidget: CommonText(
              S.current.continue_button,
              style: TextStyle(
                height: 1,
                fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                fontWeight: FontWeight.w600,
                color: context.colorScheme.mainPrimary,
              ),
            ),
            onPressed: () {
              if (isLastItem) {
                cubit.navigateToCustomTabScreen();
              } else {
                _pageController.nextPage(
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.linear,
                );
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildContentIntroStarted() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ShaderMask(
              shaderCallback: (bounds) => const LinearGradient(
                colors: AppColors.gradientCTABlue,
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ).createShader(bounds),
              child: CommonText(
                S.current.smart_note_big_ideas,
                style: TextStyle(
                  fontSize: context.isTablet ? 36 : 28.sp,
                  color: context.colorScheme.mainPrimary,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
        CommonText(
          S.current.let_note_ai,
          style: TextStyle(fontSize: context.isTablet ? 24 : 16.sp),
          textColor: context.colorScheme.mainGray,
          appFontWeight: AppFontWeight.regular,
        ),
        CommonText(
          S.current.chaos_into_clarity,
          style: TextStyle(fontSize: context.isTablet ? 24 : 16.sp),
          textColor: context.colorScheme.mainGray,
          appFontWeight: AppFontWeight.regular,
        ),
      ],
    );
  }

  Widget _buildGetStartedButton() {
    return AppCommonButton(
      width: MediaQuery.of(context).size.height * 0.4,
      height: 56,
      borderRadius: BorderRadius.circular(100.r),
      gradient: const LinearGradient(
        begin: Alignment.bottomCenter,
        end: Alignment.topCenter,
        colors: AppColors.gradientCTABlue,
      ),
      leftIcon: SvgPicture.asset(
        width: 32,
        height: 32,
        Assets.icons.icStarted,
        colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
      ),
      textWidget: Text(
        S.current.get_start,
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: context.colorScheme.mainPrimary,
        ),
      ),
      onPressed: () async {
        cubit.setGetStartedScreen(false);
        cubit.setChooseTypeScreen(true);
        AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.onboarding_get_started_next);
      },
    );
  }

  Widget _buildBackgroundImages(int currentIndex) {
    final item = onboardingLandScapeItems[currentIndex];
    if (item.isShowJsonToBgr) {
      return Lottie.asset(item.jsonUrl, fit: BoxFit.cover);
    } else {
      return BackgroundOnboardingVideoWidget(
        urlVideo: item.videoUrl,
      );
    }
  }

  Widget _buildBackgroundImagesProfessional(int currentIndex) {
    final item = onboardingLandScapeProfessionalItems[currentIndex];
    if (item.isShowJsonToBgr) {
      return Lottie.asset(item.jsonUrl, fit: BoxFit.cover);
    } else {
      return BackgroundOnboardingVideoWidget(
        urlVideo: item.videoUrl,
      );
    }
  }

  Widget _showChooseTypePage(OnboardingState state) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 83.h,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Center(
            child: CommonText(
              S.current.choose_your_note_experience,
              style: TextStyle(
                fontSize: 40,
                fontWeight: FontWeight.w700,
                color: context.colorScheme.mainPrimary,
              ),
            ),
          ),
          AppConstants.kSpacingItem8,
          Center(
            child: CommonText(
              S.current.select_your_primary_use_case,
              style: TextStyle(
                fontSize: 26,
                fontWeight: FontWeight.w400,
                color: context.colorScheme.themeWhite,
              ),
            ),
          ),
          AppConstants.kSpacingItem18,
          SvgPicture.asset(
            state.selectedTypeIndex == -1
                ? Assets.icons.icTempOb
                : state.selectedTypeIndex == 0
                    ? Assets.icons.typeStudent
                    : Assets.icons.typeBusiness,
            width: 301.w,
            height: 220.h,
          ),
          const SizedBox(height: 70),
          ChooseTypeItem(
            icon: state.selectedTypeIndex == 0
                ? Assets.icons.icStudentSelect
                : Assets.icons.icStudentUnselect,
            title: S.current.student,
            subtitle: S.current.lecture_notes_study_materials,
            isSelected: state.selectedTypeIndex == 0,
            onSelected: (selected) {
              cubit.updateSelectedType(0);
            },
          ),
          AppConstants.kSpacingItem16,
          ChooseTypeItem(
            icon: state.selectedTypeIndex == 1
                ? Assets.icons.icProSelect
                : Assets.icons.icProUnselect,
            title: S.current.professional,
            subtitle: S.current.work_notes_projects,
            isSelected: state.selectedTypeIndex == 1,
            onSelected: (selected) {
              cubit.updateSelectedType(1);
            },
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 48.w),
            child: AppCommonButton(
              disabled: state.selectedTypeIndex == -1,
              width: MediaQuery.of(context).size.height * 0.4,
              height: 56,
              borderRadius: BorderRadius.circular(100.r),
              gradient: LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: state.selectedTypeIndex == -1
                    ? [
                        AppColors.gradientCTABlue[0].withOpacity(0.38),
                        AppColors.gradientCTABlue[1].withOpacity(0.38),
                      ]
                    : AppColors.gradientCTABlue,
              ),
              textWidget: CommonText(
                S.current.continue_button,
                style: TextStyle(
                  height: 1,
                  fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                  fontWeight: FontWeight.w600,
                  color: state.selectedTypeIndex == -1
                      ? context.colorScheme.mainNeutral
                      : context.colorScheme.mainPrimary,
                ),
              ),
              onPressed: () {
                cubit.setChooseTypeScreen(false);
                AnalyticsService.logAnalyticsEventNoParam(
                    eventName: EventName.onboarding_choose_type_next);
                AnalyticsService.logAnalyticsEventNoParam(
                    eventName: state.selectedTypeIndex == 0
                        ? EventName.onboarding_1_student
                        : EventName.onboarding_1_business);
              },
            ),
          ),
          AppConstants.kSpacingItem30,
        ],
      ),
    );
  }
}
