import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/lib.dart';

class AnimatedTextEffect extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final Duration duration;

  const AnimatedTextEffect({
    Key? key,
    required this.text,
    this.style,
    this.duration = const Duration(seconds: 2),
  }) : super(key: key);

  @override
  AnimatedTextEffectState createState() => AnimatedTextEffectState();
}

class AnimatedTextEffectState extends State<AnimatedTextEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    )..repeat();

    _animation = Tween<double>(begin: -2.0, end: 2.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (Rect bounds) {
            return LinearGradient(
              colors: [
                context.colorScheme.themeWhite,
                context.colorScheme.mainGray.withOpacity(0.3),
                context.colorScheme.themeWhite,
              ],
              stops: const [0.1, 0.5, 0.9],
              begin: Alignment(1.0 + _animation.value, 0),
              end: Alignment(-1.0 + _animation.value, 0),
            ).createShader(bounds);
          },
          blendMode: BlendMode.srcATop,
          child: CommonText(
            widget.text,
            style: widget.style ??
                TextStyle(
                  fontSize: context.isTablet ? 22 : 20.sp,
                  fontWeight: FontWeight.bold,
                  color: context.colorScheme.themeWhite,
                ),
          ),
        );
      },
    );
  }
}
