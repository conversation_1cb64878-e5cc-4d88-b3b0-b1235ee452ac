import 'package:el_tooltip/el_tooltip.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import '../../../base/base_page_state.dart';
import 'cubit/upload_text_state.dart';

class UploadTextPage extends StatefulWidget {
  static const routeName = 'UploadTextPage';

  final VoidCallback? onClose;
  final VoidCallback? onCreateNoteSuccess;
  final bool isFromWelcome;

  const UploadTextPage({
    super.key,
    this.onClose,
    this.onCreateNoteSuccess,
    this.isFromWelcome = false,
  });

  @override
  State<UploadTextPage> createState() => _UploadAudioPageState();
}

class _UploadAudioPageState
    extends BasePageStateDelegate<UploadTextPage, UploadTextCubit> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.delayed(const Duration(milliseconds: 500));
      if (mounted) {
        cubit.focusNodeCreateNote.requestFocus();
      }
    });

    cubit.logEventTrackingOpenTextPage();
  }

  @override
  void dispose() {
    super.dispose();
    widget.onClose?.call();
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<UploadTextCubit, UploadTextState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent &&
          current.oneShotEvent != CreateNoteWithTextOneShotEvent.none,
      listener: (context, state) async {
        switch (state.oneShotEvent) {
          case CreateNoteWithTextOneShotEvent.none:
            break;
          case CreateNoteWithTextOneShotEvent.createNoteSuccessfully:
            // Save note model before reset state
            final noteModel = cubit.getTextNote();

            // Reset state before navigating
            cubit.resetState();

            // Call the onClose and onCreateNoteSuccess callbacks
            widget.onClose?.call();
            widget.onCreateNoteSuccess?.call();

            // Navigate
            Navigator.of(context).pop();
            final savedTabs =
                await GetIt.instance.get<LocalService>().loadSelectedItems();
            await Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => MyNoteDetailPage(
                  noteModel: noteModel,
                  isTablet: cubit.appCubit.isTablet,
                  from: NoteDetailPageFrom.textScreen,
                  savedTabs: savedTabs,
                ),
              ),
            );
            break;
          case CreateNoteWithTextOneShotEvent.onShowIAPFromText:
            Navigator.of(context).pushNamed(
              PurchasePage.routeName,
              arguments: {
                EventKey.from: PurchasePageFrom.iapCreateNote,
              },
            ).then((didPurchaseSuccess) => {
                  if (didPurchaseSuccess == true) {cubit.onSubmitText()}
                });
            break;
        }
        cubit.resetEnumState();
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBarWidget(
        isShowLeftButton: true,
        title: S.current.text,
      ),
      body: GestureDetector(
        onTap: () {
          cubit.focusNodeCreateNote.unfocus();
        },
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  margin:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      BlocBuilder<UploadTextCubit, UploadTextState>(
                        buildWhen: (previous, current) =>
                            previous.textContent != current.textContent,
                        builder: (context, state) {
                          return RawScrollbar(
                            thickness: 6,
                            thumbVisibility: false,
                            radius: Radius.circular(16.r),
                            trackRadius: Radius.circular(16.r),
                            thumbColor:
                                context.colorScheme.mainGray.withOpacity(0.6),
                            trackColor: context.colorScheme.mainNeutral
                                .withOpacity(0.6),
                            trackVisibility: false,
                            padding: const EdgeInsets.only(
                                top: 20, bottom: 2, right: 6),
                            interactive: true,
                            controller: cubit.getScrollController(),
                            child: TextField(
                              focusNode: cubit.focusNodeCreateNote,
                              controller: cubit.getTextContentController(),
                              scrollController: cubit.getScrollController(),
                              maxLines: 5,
                              autocorrect: false,
                              smartDashesType: SmartDashesType.disabled,
                              enableSuggestions: false,
                              keyboardType: TextInputType.multiline,
                              onChanged: (text) {
                                final currentPosition =
                                    cubit.getTextContentController().selection;
                                cubit.setTextContentController(text);
                                cubit.getTextContentController().selection =
                                    currentPosition;
                              },
                              cursorColor: context.colorScheme.mainBlue,
                              style: TextStyle(
                                fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                                fontWeight: FontWeight.w400,
                                color: context.colorScheme.mainPrimary,
                              ),
                              cursorRadius: const Radius.circular(2),
                              decoration: InputDecoration(
                                fillColor: context.colorScheme.mainNeutral,
                                filled: true,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16.r),
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16.r),
                                  borderSide: BorderSide.none,
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16.r),
                                  borderSide: BorderSide.none,
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: context.isTablet ? 16 : 16.w,
                                    vertical: context.isTablet ? 16 : 12.w),
                                hintText: S.current.type_or_paste_any_text_here,
                                hintStyle: TextStyle(
                                  fontSize:
                                      cubit.appCubit.isTablet ? 14 : 12.sp,
                                  fontWeight: FontWeight.w400,
                                  color: context.colorScheme.mainGray,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                      AppConstants.kSpacingItem4,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          BlocBuilder<UploadTextCubit, UploadTextState>(
                            buildWhen: (previous, current) =>
                                previous.textContent != current.textContent,
                            builder: (context, state) {
                              final charCount = state.textContent.length;
                              final isOverLimit =
                                  charCount > AppConstants.maxLengthInputText;
                              return CommonText(
                                '$charCount/${AppConstants.maxLengthInputText}',
                                style: TextStyle(
                                  fontSize:
                                      cubit.appCubit.isTablet ? 14 : 12.sp,
                                  color: isOverLimit
                                      ? AppColors.primaryRed
                                      : context.colorScheme.mainGray
                                          .withOpacity(0.38),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                      AppConstants.kSpacingItem8,
                      CommonText(
                        S.current.folder,
                        textColor: context.colorScheme.mainGray,
                        style: TextStyle(
                          fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      AppConstants.kSpacingItem8,
                      SizedBox(
                        width: double.infinity,
                        child: FolderDropdownView(
                          folders: [
                            FolderModel(
                                id: 'all_notes',
                                folderName: S.current.all_note,
                                backendId: ''),
                            ...HiveFolderService.getAllFolders()
                          ],
                          selectedFolder: cubit.selectFolderNotifier,
                          onMenuStateChanged: (isShowing) {
                            if (isShowing) {
                              cubit.focusNodeCreateNote.unfocus();
                            }
                          },
                          from: FolderDropdownFrom.text,
                        ),
                      ),

                      /// DropDown Language
                      AppConstants.kSpacingItem16,
                      Row(
                        children: [
                          CommonText(
                            S.current.summary_language,
                            textColor: context.colorScheme.mainGray,
                            style: TextStyle(
                              fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          AppConstants.kSpacingItemW4,
                          ElTooltip(
                            padding: EdgeInsets.all(8.w),
                            content: const LanguageTipsWidget(
                              isRecording: false,
                              isCheckDocAndText: true,
                            ),
                            position: ElTooltipPosition.bottomCenter,
                            color: context.colorScheme.mainBlue,
                            radius: Radius.circular(16.r),
                            child: SvgPicture.asset(
                              Assets.icons.icCommonInfoTooltip,
                              height: cubit.appCubit.isTablet ? 18 : 18.w,
                              width: cubit.appCubit.isTablet ? 18 : 18.w,
                              colorFilter: ColorFilter.mode(
                                context.colorScheme.mainPrimary,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ],
                      ),
                      AppConstants.kSpacingItem8,
                      SizedBox(
                        width: double.infinity,
                        child: LanguageDropdownView(
                          initialLanguageCode: cubit.targetTextLanguage?.code,
                          onChanged: (lang) => {cubit.setTextLanguage(lang)},
                          onMenuStateChanged: (isShowing) {
                            if (isShowing) {}
                          },
                          useCase:
                              LanguageDropdownUseCase.translateLanguageWithAuto,
                          from: LanguageDropdownFrom.text,
                        ),
                      ),

                      /// End DropDown Language
                      AppConstants.kSpacingItem16,
                      AdvancedWidget(
                        onSummaryStyleChanged: cubit.updateSummaryStyle,
                        onWritingStyleChanged: cubit.updateWritingStyle,
                        onAdditionalInstructionsChanged:
                            cubit.updateAdditionalInstructions,
                        onAdvancedToggled: cubit.setAdvancedEnabled,
                        onAdvancedChanged: (enabled) {
                          if (enabled == true) {
                            cubit.focusNodeCreateNote.unfocus();
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                top: 16.h,
                bottom:
                    MediaQuery.of(context).viewInsets.bottom > 0 ? 16.h : 32.h,
              ),
              child: BlocBuilder<UploadTextCubit, UploadTextState>(
                buildWhen: (previous, current) =>
                    previous.textContent != current.textContent,
                builder: (context, state) {
                  return _buildButtonSubmit(
                    isEnable: state.textContent.trim().isNotEmpty &&
                        state.textContent.length <=
                            AppConstants.maxLengthInputText,
                    createNoteType: NoteType.inputText,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildButtonSubmit({
    bool isEnable = true,
    required NoteType createNoteType,
  }) {
    final gradientColors = isEnable
        ? context.colorScheme.mainBlue
        : context.colorScheme.mainNeutral;
    return Center(
      child: AppCommonButton(
        width: cubit.appCubit.isTablet ? 200 : 160.w,
        height: cubit.appCubit.isTablet ? 44 : 44.h,
        borderRadius: BorderRadius.circular(24.r),
        backgroundColor: gradientColors,
        textWidget: Text(
          S.current.add_to_notes,
          style: TextStyle(
            fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
            fontWeight: FontWeight.w500,
            color: isEnable
                ? context.colorScheme.themeWhite
                : context.colorScheme.mainPrimary.withOpacity(0.38),
          ),
        ),
        onPressed: () async {
          if (isEnable) {
            cubit.focusNodeCreateNote.unfocus();
            cubit.onSubmitText();
          }
        },
      ),
    );
  }
}
