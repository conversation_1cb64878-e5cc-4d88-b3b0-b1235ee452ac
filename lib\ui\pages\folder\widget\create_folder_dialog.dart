import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

Future<dynamic> showCreateFolderDialog(
  BuildContext context, {
  required TextEditingController controller,
  required VoidCallback onPressed,
  Function()? onClosed,
  required String title,
  required String contentButton,
  required String hintText,
  required String initialValue,
}) {
  return showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) => CreateFolderDialog(
      controller: controller,
      onPressed: onPressed,
      onClosed: onClosed,
      title: title,
      contentButton: contentButton,
      hintText: hintText,
      initialValue: initialValue,
    ),
  );
}

class CreateFolderDialog extends StatefulWidget {
  final TextEditingController controller;
  final VoidCallback onPressed;
  final String title;
  final String contentButton;
  final String hintText;
  final String initialValue;
  final Function()? onClosed;

  const CreateFolderDialog({
    Key? key,
    required this.controller,
    required this.onPressed,
    required this.onClosed,
    required this.title,
    required this.contentButton,
    required this.hintText,
    required this.initialValue,
  }) : super(key: key);

  @override
  State<CreateFolderDialog> createState() => _CreateFolderDialogState();
}

class _CreateFolderDialogState extends State<CreateFolderDialog> {
  late FocusNode _focusNode;
  ValueNotifier<bool> isShowErrorText = ValueNotifier(false);
  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    // Set the initial value of the controller
    widget.controller.text = widget.initialValue;
    // Request focus after the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(_focusNode);
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Dialog(
          backgroundColor: context.colorScheme.mainSecondary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24.r),
          ),
          insetPadding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: CommonText(
                    widget.title,
                    style: TextStyle(
                      fontSize: context.isTablet ? 22 : 20.sp,
                      fontWeight: FontWeight.w600,
                      color: context.colorScheme.mainPrimary,
                    ),
                  ),
                ),
                AppConstants.kSpacingItem8,
                CommonText(
                  S.current.folder,
                  style: TextStyle(
                    fontSize: context.isTablet ? 16 : 14.sp,
                    fontWeight: FontWeight.w400,
                    color: context.colorScheme.mainGray,
                  ),
                ),
                AppConstants.kSpacingItem8,
                Container(
                  decoration: BoxDecoration(
                    color: context.colorScheme.mainNeutral,
                    borderRadius: BorderRadius.circular(24.r),
                  ),
                  child: TextField(
                    controller: widget.controller,
                    focusNode: _focusNode,
                    style: TextStyle(
                      fontSize: context.isTablet ? 16 : 14.sp,
                      fontWeight: FontWeight.w500,
                      color: context.colorScheme.mainPrimary,
                    ),
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(50),
                    ],
                    cursorColor: context.colorScheme.mainBlue,
                    cursorHeight: context.isTablet ? 16 : 14.h,
                    decoration: InputDecoration(
                      contentPadding: EdgeInsets.only(
                          left: 8.w, right: 12.w, top: 8.h, bottom: 8.h),
                      border: InputBorder.none,
                      prefixIcon: Padding(
                        padding: EdgeInsets.only(
                          right: context.isTablet ? 8 : 8.w,
                          left: context.isTablet ? 8 : 8.w,
                        ),
                        child: Container(
                          width: context.isTablet ? 32 : 32.w,
                          height: context.isTablet ? 32 : 32.w,
                          decoration: BoxDecoration(
                            color: context.colorScheme.mainPrimary
                                .withOpacity(0.1),
                            borderRadius: BorderRadius.circular(24.r),
                          ),
                          padding: EdgeInsets.all(context.isTablet ? 6 : 6.w),
                          child: SvgPicture.asset(
                            Assets.icons.icFolderMini,
                            colorFilter: ColorFilter.mode(
                              context.colorScheme.mainGray,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                      prefixIconConstraints: const BoxConstraints(
                        minWidth: 0,
                        minHeight: 0,
                      ),
                      hintText: widget.hintText,
                      hintStyle: TextStyle(
                        fontSize: context.isTablet ? 14 : 12.sp,
                        color: context.colorScheme.mainGray.withOpacity(0.38),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    onChanged: (value) {
                      isShowErrorText.value = value.trim().length == 50;
                      (context as Element).markNeedsBuild();
                    },
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: isShowErrorText,
                  builder: (context, isShowErrorText, child) {
                    return isShowErrorText
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              AppConstants.kSpacingItem4,
                              CommonText(
                                S.current.text_must_not_exceed_50_chars,
                                style: TextStyle(
                                  fontSize: context.isTablet ? 14 : 12.sp,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.primaryRed,
                                ),
                              ),
                            ],
                          )
                        : const SizedBox.shrink();
                  },
                ),
                SizedBox(height: 16.h),
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          widget.controller.text = '';
                          widget.onClosed?.call();
                          Navigator.pop(context);
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.r),
                            color: context.colorScheme.mainNeutral,
                          ),
                          height: context.isTablet ? 48 : 44.h,
                          alignment: Alignment.center,
                          child: CommonText(
                            S.current.cancel,
                            style: TextStyle(
                              color: context.colorScheme.mainPrimary,
                              fontWeight: FontWeight.w500,
                              fontSize: context.isTablet ? 18 : 16.sp,
                            ),
                          ),
                        ),
                      ),
                    ),
                    AppConstants.kSpacingItemW8,
                    Expanded(
                      child: GestureDetector(
                        onTap: widget.controller.text.trim().isEmpty
                            ? null
                            : () {
                                widget.onPressed();
                                Navigator.pop(context);
                              },
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.r),
                            color: widget.controller.text.trim().isEmpty
                                ? context.colorScheme.mainBlue.withOpacity(0.38)
                                : context.colorScheme.mainBlue,
                          ),
                          height: context.isTablet ? 48 : 44.h,
                          alignment: Alignment.center,
                          child: CommonText(
                            widget.contentButton,
                            style: TextStyle(
                              color: widget.controller.text.trim().isEmpty
                                  ? AppColors.white.withOpacity(0.38)
                                  : AppColors.white,
                              fontWeight: FontWeight.w500,
                              fontSize: context.isTablet ? 17 : 15.sp,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
