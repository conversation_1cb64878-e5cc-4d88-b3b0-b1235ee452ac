import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/youtube/cubit/youtube_state.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:el_tooltip/el_tooltip.dart';
import 'package:get_it/get_it.dart';

class YoutubePage extends StatefulWidget {
  static const routeName = 'YoutubePage';

  final String? content;
  final bool isFromWelcome;
  final VoidCallback? onClose;
  final VoidCallback? onCreateNoteSuccess;

  const YoutubePage({
    super.key,
    this.content,
    this.isFromWelcome = false,
    this.onClose,
    this.onCreateNoteSuccess,
  });

  @override
  State<YoutubePage> createState() => _YoutubePageState();
}

class _YoutubePageState
    extends BasePageStateDelegate<YoutubePage, YoutubeCubit> {
  @override
  void initState() {
    super.initState();
    if (widget.content != null) {
      cubit.setWebLinkTextController(widget.content!);
      cubit.onWebLinkTextChanged(widget.content!);
    }
    cubit.focusNodeCreateNote.requestFocus();
    cubit.logEventTrackingOpenLinkPage();
  }

  @override
  void didUpdateWidget(covariant YoutubePage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.content != null) {
      cubit.setWebLinkTextController(widget.content!);
      cubit.onWebLinkTextChanged(widget.content!);
    }
    widget.onClose?.call();
  }

  @override
  void dispose() {
    super.dispose();
    widget.onClose?.call();
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<YoutubeCubit, YoutubeState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent &&
          current.oneShotEvent != CreateNoteOneShotEventYoutube.none,
      listener: (context, state) async {
        switch (state.oneShotEvent) {
          case CreateNoteOneShotEventYoutube.none:
            break;
          case CreateNoteOneShotEventYoutube.createNoteSuccessfully:
            // Save note model before reset state
            final noteModel = cubit.getYoutubeNote();

            // Reset state before navigating
            cubit.resetState();

            // Call the onClose and onCreateNoteSuccess callbacks
            widget.onClose?.call();
            widget.onCreateNoteSuccess?.call();

            // Navigate
            Navigator.of(context).pop();
            final savedTabs =
                await GetIt.instance.get<LocalService>().loadSelectedItems();
            await Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => MyNoteDetailPage(
                  noteModel: noteModel,
                  isTablet: cubit.appCubit.isTablet,
                  from: NoteDetailPageFrom.createYoutubeNoteScreen,
                  savedTabs: savedTabs,
                ),
              ),
            );
            break;
          case CreateNoteOneShotEventYoutube.onShowIAPFromYoutube:
            Navigator.of(context).pushNamed(
              PurchasePage.routeName,
              arguments: {
                EventKey.from: PurchasePageFrom.iapCreateNote,
              },
            ).then((didPurchaseSuccess) => {
                  if (didPurchaseSuccess == true) {cubit.onSubmitWebLink()}
                });
            break;
          case CreateNoteOneShotEventYoutube.onShowSelectLanguageDialog:
            showBlackCupertinoDialog(
              context: context,
              title: '',
              message: S.current.please_select_a_youtube_language,
              confirmButton: S.current.ok,
              onConfirm: () {},
              confirmButtonTextColor: context.colorScheme.mainBlue,
            );
            break;
        }
        cubit.resetEnumState();
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBarWidget(
          isShowLeftButton: true,
          title: S.current.web_link,
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildWebLinkScreen(),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  top: 16.h,
                  bottom: MediaQuery.of(context).viewInsets.bottom > 0
                      ? 16.h
                      : context.isTablet
                          ? 32
                          : 32.h),
              child: BlocBuilder<YoutubeCubit, YoutubeState>(
                buildWhen: (previous, current) =>
                    previous.youtubeLinkIsReady != current.youtubeLinkIsReady,
                builder: (context, state) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildButtonOpenYoutube(),
                      const SizedBox(width: 12),
                      _buildButtonAddToNotes(state.youtubeLinkIsReady),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ));
  }

  Widget _buildWebLinkScreen() {
    return GestureDetector(
      onTap: () {
        cubit.focusNodeCreateNote.unfocus();
      },
      child: Container(
        margin: cubit.appCubit.isTablet
            ? const EdgeInsets.all(36)
            : EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: cubit.appCubit.isTablet ? 64 : 64.h,
              width: double.infinity,
              decoration: BoxDecoration(
                color: context.colorScheme.mainNeutral,
                borderRadius: BorderRadius.circular(64.r),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(
                          left: 16.w, right: 8.w, top: 16.h, bottom: 16.h),
                      height: 32.h,
                      alignment: Alignment.center,
                      child: TextField(
                        cursorColor: context.colorScheme.mainBlue,
                        cursorRadius: Radius.circular(2.r),
                        focusNode: cubit.focusNodeCreateNote,
                        style: TextStyle(
                          decorationThickness: 0,
                          fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                          fontWeight: FontWeight.w500,
                          color: context.colorScheme.mainPrimary,
                        ),
                        onChanged: (text) {
                          cubit.onWebLinkTextChanged(text);
                        },
                        decoration: InputDecoration(
                          isDense: true,
                          hintText: S.current.paste_url_here,
                          hintStyle: TextStyle(
                            height: 1,
                            fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                            fontWeight: FontWeight.w400,
                            color: context.colorScheme.mainPrimary
                                .withOpacity(0.38),
                          ),
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                        ),
                        controller: cubit.getWebLinkTextController(),
                      ),
                    ),
                  ),
                  ValueListenableBuilder<TextEditingValue>(
                      valueListenable: cubit.getWebLinkTextController(),
                      builder: (context, value, child) {
                        return value.text.isEmpty
                            ? GestureDetector(
                                onTap: () {
                                  _pasteTextFromClipboard();
                                },
                                child: Container(
                                  margin:
                                      EdgeInsets.symmetric(horizontal: 16.w),
                                  height: 32.h,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 16.w, vertical: 8.h),
                                  decoration: ShapeDecoration(
                                    color: context.colorScheme.mainBlue,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(24.r),
                                    ),
                                  ),
                                  child: Center(
                                    child: CommonText(
                                      height: 1,
                                      S.current.paste,
                                      style: TextStyle(
                                        fontSize: cubit.appCubit.isTablet
                                            ? 14
                                            : 12.sp,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            : GestureDetector(
                                onTap: () {
                                  cubit.getWebLinkTextController().clear();
                                },
                                child: Padding(
                                  padding: EdgeInsets.only(right: 16.w),
                                  child: SvgPicture.asset(
                                    Assets.icons.icCloseWhite,
                                    width: 24,
                                    height: 24,
                                    colorFilter: ColorFilter.mode(
                                      context.colorScheme.mainPrimary,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              );
                      }),
                ],
              ),
            ),
            AppConstants.kSpacingItem8,
            Center(
              child: CommonText(
                S.current.support_youtube_and_more,
                style: TextStyle(
                  fontSize: cubit.appCubit.isTablet ? 14 : 12.sp,
                  fontWeight: FontWeight.w400,
                  color: context.colorScheme.mainGray.withOpacity(0.86),
                ),
                maxLines: 1,
              ),
            ),
            AppConstants.kSpacingItem16,
            CommonText(
              S.current.folder,
              textColor: context.colorScheme.mainGray,
              style: TextStyle(
                fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
            AppConstants.kSpacingItem8,
            SizedBox(
              width: double.infinity,
              child: FolderDropdownView(
                folders: [
                  FolderModel(
                      id: 'all_notes',
                      folderName: S.current.all_note,
                      backendId: ''),
                  ...HiveFolderService.getAllFolders()
                ],
                selectedFolder: cubit.tabWebLinkChangeFolderNotifier,
                onMenuStateChanged: (isShowing) {
                  if (isShowing) {
                    cubit.focusNodeCreateNote.unfocus();
                  }
                },
                from: FolderDropdownFrom.weblink,
              ),
            ),
            AppConstants.kSpacingItem16,
            BlocBuilder<YoutubeCubit, YoutubeState>(
              buildWhen: (previous, current) =>
                  previous.isSummaryLanguageYoutubeMode !=
                  current.isSummaryLanguageYoutubeMode,
              builder: (context, state) => SizedBox(
                  width: double.infinity,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CommonText(
                            S.current.summary_language,
                            style: TextStyle(
                              fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                              fontWeight: FontWeight.w400,
                              color: context.colorScheme.mainGray,
                            ),
                          ),
                          AppConstants.kSpacingItemW4,
                          ElTooltip(
                            padding: EdgeInsets.all(8.w),
                            content: const LanguageTipsWidget(
                              isRecording: false,
                              isCheckDocAndText: true,
                            ),
                            position: ElTooltipPosition.bottomCenter,
                            color: context.colorScheme.mainBlue,
                            radius: Radius.circular(16.r),
                            child: SvgPicture.asset(
                              Assets.icons.icCommonInfoTooltip,
                              height: cubit.appCubit.isTablet ? 18 : 18.w,
                              width: cubit.appCubit.isTablet ? 18 : 18.w,
                              colorFilter: ColorFilter.mode(
                                context.colorScheme.mainPrimary,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ],
                      ),
                      AppConstants.kSpacingItem8,
                      LanguageDropdownView(
                        useCase: state.isSummaryLanguageYoutubeMode
                            ? LanguageDropdownUseCase.defaultTranslateLanguage
                            : LanguageDropdownUseCase.translateLanguageWithAuto,
                        from: LanguageDropdownFrom.webLink,
                        initialLanguageCode:
                            cubit.targetYoutubeLinkLanguage?.code,
                        onChanged: (lang) =>
                            {cubit.setYoutubeLinkLanguage(lang)},
                        onMenuStateChanged: (isShowing) {
                          if (isShowing) {
                            cubit.focusNodeCreateNote.unfocus();
                          }
                        },
                      ),
                    ],
                  )),
            ),
            AppConstants.kSpacingItem16,
            AdvancedWidget(
              onSummaryStyleChanged: cubit.updateSummaryStyle,
              onWritingStyleChanged: cubit.updateWritingStyle,
              onAdditionalInstructionsChanged:
                  cubit.updateAdditionalInstructions,
              onAdvancedToggled: cubit.setAdvancedEnabled,
              onAdvancedChanged: (enabled) {
                if (enabled == true) {
                  cubit.focusNodeCreateNote.unfocus();
                }
              },
            ),
            AppConstants.kSpacingItem32,
          ],
        ),
      ),
    );
  }

  Future<void> _pasteTextFromClipboard() async {
    final clipboardData = await Clipboard.getData('text/plain');
    cubit.getWebLinkTextController().text = clipboardData?.text ?? '';
    cubit.onWebLinkTextChanged(clipboardData?.text ?? '');
  }

  void _launchUrl(String url) async {
    try {
      launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      launchUrl(
        Uri.parse(url),
      );
    }
  }

  Widget _buildButtonOpenYoutube() {
    return Container(
      alignment: Alignment.center,
      child: AppCommonButton(
        width: cubit.appCubit.isTablet ? 200 : 160.w,
        height: cubit.appCubit.isTablet ? 44 : 44.h,
        borderRadius: BorderRadius.circular(24.r),
        backgroundColor: AppColors.primaryRed,
        textWidget: Text(
          S.current.open_youtube,
          style: TextStyle(
            fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
            fontWeight: FontWeight.w500,
            color: context.colorScheme.themeWhite,
          ),
        ),
        onPressed: () {
          _launchUrl('https://youtube.com');
          AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.weblink_youtube_open,
          );
        },
      ),
    );
  }

  Widget _buildButtonAddToNotes(bool isEnable) {
    final gradientColors = isEnable
        ? context.colorScheme.mainBlue
        : context.colorScheme.mainNeutral;
    return Container(
      alignment: Alignment.center,
      child: AppCommonButton(
          width: cubit.appCubit.isTablet ? 200 : 160.w,
          height: cubit.appCubit.isTablet ? 44 : 44.h,
          borderRadius: BorderRadius.circular(24.r),
          backgroundColor: gradientColors,
          textWidget: Text(
            S.current.add_to_notes,
            style: TextStyle(
              fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
              fontWeight: FontWeight.w500,
              color: isEnable
                  ? context.colorScheme.themeWhite
                  : context.colorScheme.mainPrimary.withOpacity(0.38),
            ),
          ),
          onPressed: () async {
            if (isEnable) {
              cubit.focusNodeCreateNote.unfocus();
              cubit.onSubmitWebLink();
            }
          }),
    );
  }
}
