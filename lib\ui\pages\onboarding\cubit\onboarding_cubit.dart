import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

import '../../../../base/app/app_state.dart';
import 'onboarding_state.dart';

class OnboardingCubit extends BaseCubit<OnboardingState> {
  OnboardingCubit(super.initialState);

  void updateCurrentIndex(int index) {
    emit(state.copyWith(currentIndex: index));
  }

  void setGetStartedScreen(bool value) {
    emit(state.copyWith(isGetStartedScreen: value));
  }

  void setChooseTypeScreen(bool value) {
    emit(state.copyWith(isChooseTypeScreen: value));
  }

  void setCustomTabScreen(bool value) {
    emit(state.copyWith(isCustomTabScreen: value));
  }

  void updateSelectedType(int index) {
    emit(state.copyWith(
      selectedTypeIndex: index,
    ));
  }

  void navigateToCustomTabScreen() {
    setCustomTabScreen(true);
  }

  void finishOnboarding() {
    //Clear any existing auth data first
    //GetIt.instance.get<LocalService>().clear();
    
    // Save onboarding status
    GetIt.instance.get<LocalService>().setOnboardingUserType(
        state.selectedTypeIndex == 0 ? 'student' : 'business');
    GetIt.instance.get<LocalService>().setOnboardingStatus(true);
    
    // Navigate to login screen
    appCubit.setCurrentScreen(AppScreen.loginFromOnboarding);
  }

  void trackingEventTypeStudent(int page) {
    switch (page) {
      case 0:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.onboarding_2_student,
        );
        break;
      case 1:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.onboarding_3_student,
        );
        break;
      case 2:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.onboarding_4_student,
        );
        break;
      case 3:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.onboarding_5_student,
        );
        break;
      default:
        break;
    }
  }

  void trackingEventTypeProfessional(int page) {
    switch (page) {
      case 0:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.onboarding_2_business,
        );
        break;
      case 1:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.onboarding_3_business,
        );
        break;
      case 2:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.onboarding_4_business,
        );
        break;
      default:
        break;
    }
  }
}
