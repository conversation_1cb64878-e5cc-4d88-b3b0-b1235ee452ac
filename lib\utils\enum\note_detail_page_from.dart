import 'package:note_x/lib.dart';

enum NoteDetailPageFrom {
  recordAudioScreen,
  createNoteScreen,
  documentScreen,
  textScreen,
  createAudioNoteScreen,
  createYoutubeNoteScreen,
  homeScreen,
  folderDetailScreen,
  notification,
  searchScreen,
  imageScreen,
}

enum ExportNoteFormat {
  docx,
  pdf,
  srt,
  ;

  String get displayFormat {
    switch (this) {
      case ExportNoteFormat.docx:
        return 'docx';
      case ExportNoteFormat.pdf:
        return 'pdf';
      case ExportNoteFormat.srt:
        return 'srt';
    }
  }

  String get displayName {
    switch (this) {
      case ExportNoteFormat.docx:
        return S.current.word_docx;
      case ExportNoteFormat.pdf:
        return S.current.pdf_pdf;
      case ExportNoteFormat.srt:
        return S.current.sub_rip;
    }
  }
}

enum ExportType {
  summary,
  transcript,
  quiz,
  flashcards;

  String get displayType {
    switch (this) {
      case ExportType.summary:
        return 'summary';
      case ExportType.transcript:
        return 'transcript';
      case ExportType.quiz:
        return 'quiz';
      case ExportType.flashcards:
        return 'flashcards';
    }
  }

  String get displayTitle {
    switch (this) {
      case ExportType.summary:
        return S.current.export_pdf;
      case ExportType.transcript:
        return S.current.export_transcript;
      case ExportType.quiz:
        return S.current.export_quiz;
      case ExportType.flashcards:
        return S.current.export_flashcard;
    }
  }

  List<ExportNoteFormat> get exportFormatList {
    switch (this) {
      case ExportType.transcript:
        return [
          ExportNoteFormat.docx,
          ExportNoteFormat.pdf,
          ExportNoteFormat.srt
        ];
      default:
        return [ExportNoteFormat.docx, ExportNoteFormat.pdf];
    }
  }

  List<ExportNoteFormat> getExportFormatList(String noteType) {
    if (this == ExportType.transcript &&
        (noteType == NoteType.youtube.backendType ||
            noteType == NoteType.uploadAudio.backendType ||
            noteType == NoteType.recordAudio.backendType)) {
      return [
        ExportNoteFormat.docx,
        ExportNoteFormat.pdf,
        ExportNoteFormat.srt
      ];
    }
    return [ExportNoteFormat.docx, ExportNoteFormat.pdf];
  }

  String getAnalyticsEventName(ExportNoteFormat format) {
    switch (this) {
      case ExportType.summary:
        return format == ExportNoteFormat.pdf
            ? EventName.note_detail_export_pdf
            : EventName.note_detail_export_docx;
      case ExportType.transcript:
        return format == ExportNoteFormat.pdf
            ? EventName.note_transcript_export_pdf
            : EventName.note_transcript_export_docx;
      case ExportType.quiz:
        return format == ExportNoteFormat.pdf
            ? EventName.note_quiz_export_pdf
            : EventName.note_quiz_export_docx;
      case ExportType.flashcards:
        return format == ExportNoteFormat.pdf
            ? EventName.note_flashcard_export_pdf
            : EventName.note_flashcard_export_docx;
    }
  }
}
