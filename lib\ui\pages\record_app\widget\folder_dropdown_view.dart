import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:note_x/lib.dart';

class FolderDropdownView extends StatefulWidget {
  final List<FolderModel> folders;
  final ValueNotifier<FolderModel> selectedFolder;
  final bool isDisabled;
  final Function(bool)? onMenuStateChanged;
  final FolderDropdownFrom from;

  const FolderDropdownView({
    Key? key,
    required this.folders,
    required this.selectedFolder,
    this.isDisabled = false,
    this.onMenuStateChanged,
    required this.from,
  }) : super(key: key);

  @override
  State<FolderDropdownView> createState() => _FolderDropdownViewState();
}

class _FolderDropdownViewState extends State<FolderDropdownView> {
  final LayerLink _layerLink = LayerLink();
  final ScrollController _scrollController = ScrollController();
  OverlayEntry? _overlayEntry;
  bool _isShowingMenu = false;

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: widget.isDisabled ? null : _toggleMenu,
        child: _buildSelector(),
      ),
    );
  }

  Widget _buildSelector() {
    return ValueListenableBuilder<FolderModel>(
      valueListenable: widget.selectedFolder,
      builder: (context, selectedFolder, _) {
        return Container(
          height: context.isTablet ? 48 : 48.h,
          padding: context.isTablet
              ? const EdgeInsets.symmetric(horizontal: 12, vertical: 8)
              : EdgeInsets.only(
                  left: 8.w,
                  right: 12.w,
                  top: 8.h,
                  bottom: 8.h,
                ),
          decoration: BoxDecoration(
            color: context.colorScheme.mainNeutral,
            borderRadius: BorderRadius.circular(24.r),
          ),
          child: Row(
            children: [
              SvgPicture.asset(
                  context.isDarkMode
                      ? Assets.icons.icFlipFolderMini
                      : Assets.icons.icFolderDropdownLightMode,
                  height: context.isTablet ? 32 : 32.w,
                  width: context.isTablet ? 32 : 32.w),
              AppConstants.kSpacingItemW8,
              Expanded(
                child: CommonText(
                  selectedFolder.folderName,
                  style: TextStyle(
                    fontSize: context.isTablet ? 16 : 14.sp,
                    fontWeight: FontWeight.w500,
                    color: context.colorScheme.mainPrimary,
                  ),
                  maxLines: 1,
                ),
              ),
              if (!widget.isDisabled) ...[
                SizedBox(width: 8.w),
                SvgPicture.asset(
                  width: 20.w,
                  height: 20.h,
                  Assets.icons.icCreateNoteDropDown,
                  colorFilter: ColorFilter.mode(
                    context.colorScheme.mainPrimary,
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  void _toggleMenu() {
    setState(() {
      _isShowingMenu = !_isShowingMenu;
      if (_isShowingMenu) {
        _showOverlay();
      } else {
        _hideOverlay();
      }
      widget.onMenuStateChanged?.call(_isShowingMenu);
    });
  }

  void _showOverlay() {
    if (!mounted) return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: _toggleMenu,
              behavior: HitTestBehavior.opaque,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          Positioned(
            width: size.width,
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: Offset(0, size.height + 8.h),
              child: Material(
                elevation: 4.0,
                color: context.colorScheme.mainNeutral,
                borderRadius: BorderRadius.circular(16.r),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16.r),
                  child: Container(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.3,
                      maxWidth: MediaQuery.of(context).size.width - 32.w,
                    ),
                    child: CupertinoScrollbar(
                      thumbVisibility: true,
                      controller: _scrollController,
                      child: ListView.builder(
                        controller: _scrollController,
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        physics: const BouncingScrollPhysics(),
                        itemCount: widget.folders.length,
                        itemBuilder: _buildFolderItem,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  Widget _buildFolderItem(BuildContext context, int index) {
    final folder = widget.folders[index];
    return ValueListenableBuilder(
      valueListenable: widget.selectedFolder,
      builder: (context, selected, _) {
        final isSelected = folder.id == selected.id;
        return GestureDetector(
          onTap: () {
            widget.selectedFolder.value = folder;
            AnalyticsService.logAnalyticsEventNoParam(
                eventName: widget.from.eventName);
            _toggleMenu();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            height: context.isTablet ? 44 : 44.h,
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
              color: isSelected
                  ? context.colorScheme.mainSecondary
                  : context.colorScheme.mainNeutral,
              borderRadius: BorderRadius.vertical(
                top: index == 0 ? Radius.circular(16.r) : Radius.zero,
                bottom: index == widget.folders.length - 1
                    ? Radius.circular(16.r)
                    : Radius.zero,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: CommonText(
                    folder.folderName,
                    style: TextStyle(
                      fontSize: context.isTablet ? 16 : 14.sp,
                      fontWeight: FontWeight.w400,
                      color: context.colorScheme.mainPrimary,
                    ),
                    maxLines: 1,
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check,
                    size: 24,
                    color: context.colorScheme.mainBlue,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _hideOverlay() {
    if (!mounted) return;
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _hideOverlay();
    _scrollController.dispose();
    super.dispose();
  }
}

enum FolderDropdownFrom {
  recording,
  audio,
  weblink,
  document,
  text,
  image,
  camera;

  String get eventName {
    switch (this) {
      case FolderDropdownFrom.recording:
        return EventName.record_audio_folder_selected;
      case FolderDropdownFrom.audio:
        return EventName.upload_audio_folder_selected;
      case FolderDropdownFrom.weblink:
        return EventName.weblink_folder_selected;
      case FolderDropdownFrom.document:
        return EventName.document_folder_selected;
      case FolderDropdownFrom.text:
        return EventName.text_folder_selected;
      case FolderDropdownFrom.image:
        return EventName.uploadImageFolderSelected;
      case FolderDropdownFrom.camera:
        return EventName.cameraFolderSelected;
      default:
        return EventName.unknown_folder_selected;
    }
  }
}
