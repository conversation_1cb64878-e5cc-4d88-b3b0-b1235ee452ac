import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:note_x/lib.dart';
import 'package:webview_flutter/webview_flutter.dart';

class MindMapPage extends StatefulWidget {
  final bool isFullScreen;
  final NoteModel note;
  final MyNoteDetailCubit cubit;

  const MindMapPage({
    Key? key,
    this.isFullScreen = false,
    required this.note,
    required this.cubit,
  }) : super(key: key);

  @override
  State<MindMapPage> createState() => _MindMapPageState();
}

class _MindMapPageState extends State<MindMapPage> {
  late final WebViewController controller;
  final ValueNotifier<Map<ExportFormat, bool>> loadingStates = ValueNotifier({
    ExportFormat.png: false,
    ExportFormat.jpeg: false,
    ExportFormat.pdf: false,
    ExportFormat.docx: false,
  });

  @override
  void initState() {
    super.initState();

    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {},
          onPageFinished: (String url) {
            if (mounted) _injectThemeCSS();
            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted) _injectThemeCSS();
            });
          },
          onWebResourceError: (WebResourceError error) {},
        ),
      )
      ..loadRequest(Uri.parse(
          '${Env.instance.baseURL}v1/share/mindmap/${widget.note.backendNoteId.isNotEmpty ? widget.note.backendNoteId : widget.note.id}'));

    if (widget.isFullScreen) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }

    AnalyticsService.logEventScreenView(
      screenClass: EventScreenClass.noteDetailPage,
      screenName: EventScreenName.note_mindmap,
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    controller.setBackgroundColor(context.colorScheme.mainSecondary);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) _injectThemeCSS();
    });
  }

  /// Injects CSS to apply theme colors based on isDarkMode()
  void _injectThemeCSS() {
    try {
      final String cssContent = _getThemeCSS();
      final String jsScript = '''
        (function() {
          try {
            // Remove any existing theme style element
            const existingStyle = document.getElementById('notex-theme-style');
            if (existingStyle) {
              existingStyle.remove();
            }

            // Create and inject new style element
            const style = document.createElement('style');
            style.id = 'notex-theme-style';
            style.textContent = `$cssContent`;
            document.head.appendChild(style);

            // Also update the inline style in the HTML if it exists
            const bodyElement = document.body;
            if (bodyElement) {
              bodyElement.style.backgroundColor =
                  '${context.isDarkMode ? "#000000" : "#FFFFFF"}';
              bodyElement.style.color =
                  '${context.isDarkMode ? "#FFFFFF" : "#000000"}';
            }

            // Update SVG background if it exists
            const svgElements = document.querySelectorAll('svg.markmap');
            if (svgElements && svgElements.length > 0) {
              for (let i = 0; i < svgElements.length; i++) {
                svgElements[i].style.backgroundColor =
                    '${context.isDarkMode ? "#000000" : "#FFFFFF"}';
              }
            }

            console.log(
                'NoteX theme applied: ${context.isDarkMode ? "dark" : "light"}');
          } catch (error) {
            console.error('Error applying NoteX theme:', error);
          }
        })();
      ''';

      controller.runJavaScript(jsScript);
    } catch (e) {
      debugPrint('Error injecting theme CSS: $e');
    }
  }

  /// Returns CSS content based on current theme
  String _getThemeCSS() {
    if (context.isDarkMode) {
      // Dark mode theme
      return '''
        body {
          background-color: #000000;
          color: #FFFFFF;
          font-family: Montserrat, NeverMind, sans-serif, "Microsoft YaHei", "PingFang SC", "Microsoft JhengHei", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }
        svg.markmap {
          background-color: #000000;
        }
        .markmap-node {
          fill: #FFFFFF;
        }
        .markmap-node-circle {
          fill: #333333;
          stroke: #FFFFFF;
        }
        .markmap-link {
          stroke: #666666;
        }
        .markmap-node-text {
          fill: #FFFFFF;
        }
        a {
          color: #4A9DFF;
        }
        code {
          background-color: #333333;
          color: #FFFFFF;
          border-radius: 3px;
          padding: 2px 4px;
        }
      ''';
    } else {
      // Light mode theme
      return '''
        body {
          background-color: #FFFFFF;
          color: #000000;
          font-family: Montserrat, NeverMind, sans-serif, "Microsoft YaHei", "PingFang SC", "Microsoft JhengHei", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }
        svg.markmap {
          background-color: #FFFFFF;
        }
        .markmap-node {
          fill: #000000;
        }
        .markmap-node-circle {
          fill: #FFFFFF;
          stroke: #000000;
        }
        .markmap-link {
          stroke: #999999;
        }
        .markmap-node-text {
          fill: #000000;
        }
        a {
          color: #0066CC;
        }
        code {
          background-color: #F0F0F0;
          color: #000000;
          border-radius: 3px;
          padding: 2px 4px;
        }
      ''';
    }
  }

  @override
  void dispose() {
    loadingStates.dispose();
    if (widget.isFullScreen) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    }
    controller.clearCache();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: context.colorScheme.mainBackground,
      child: SafeArea(
        child: Scaffold(
          backgroundColor: context.colorScheme.mainBackground,
          body: Stack(
            children: [
              WebViewWidget(
                gestureRecognizers: {
                  Factory<ScaleGestureRecognizer>(
                      () => ScaleGestureRecognizer()),
                  Factory<VerticalDragGestureRecognizer>(
                      () => VerticalDragGestureRecognizer()),
                  Factory<PanGestureRecognizer>(
                    () => PanGestureRecognizer(),
                  ),
                },
                controller: controller,
              ),
              Positioned(
                left: 16,
                top: 16,
                child: InkWell(
                  onTap: () {
                    if (widget.isFullScreen) {
                      Navigator.of(context).pop();
                      AnalyticsService.logAnalyticsEventNoParam(
                        eventName: EventName.note_mindmap_full_screen_exit,
                      );
                    } else {
                      Navigator.of(context).push(
                        CupertinoPageRoute(
                          fullscreenDialog: true,
                          builder: (context) => MindMapPage(
                            isFullScreen: true,
                            note: widget.note,
                            cubit: widget.cubit,
                          ),
                        ),
                      );
                      AnalyticsService.logAnalyticsEventNoParam(
                        eventName: EventName.note_mindmap_full_screen,
                      );
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.all(context.isTablet ? 12 : 12.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: context.colorScheme.mainNeutral,
                    ),
                    child: widget.isFullScreen
                        ? SvgPicture.asset(
                            Assets.icons.icFullscreenExit,
                            colorFilter: ColorFilter.mode(
                              context.colorScheme.mainPrimary,
                              BlendMode.srcIn,
                            ),
                          )
                        : SvgPicture.asset(
                            Assets.icons.icFullscreen,
                            colorFilter: ColorFilter.mode(
                              context.colorScheme.mainPrimary,
                              BlendMode.srcIn,
                            ),
                          ),
                  ),
                ),
              ),
              Positioned(
                right: 16,
                top: 16,
                child: Container(
                  padding: EdgeInsets.all(context.isTablet ? 12 : 12.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.r),
                    color: context.colorScheme.mainNeutral,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      InkWell(
                        onTap: () {
                          controller.reload();
                          AnalyticsService.logAnalyticsEventNoParam(
                            eventName: EventName.note_mindmap_reload,
                          );
                        },
                        child: SvgPicture.asset(
                          Assets.icons.icReloadWhite,
                          width: context.isTablet ? 20 : 20.w,
                          height: context.isTablet ? 20 : 20.w,
                          colorFilter: ColorFilter.mode(
                            context.colorScheme.mainPrimary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                      SizedBox(height: 12.h),
                      Container(
                        width: 20.w,
                        height: 0.5,
                        color: context.colorScheme.mainPrimary.withOpacity(0.1),
                      ),
                      SizedBox(height: 12.h),
                      InkWell(
                        onTap: () {
                          _showModalBottomSheetExportMindMap(context);
                          AnalyticsService.logAnalyticsEventNoParam(
                            eventName: EventName.note_mindmap_export,
                          );
                        },
                        child: SvgPicture.asset(
                          Assets.icons.icDownload,
                          colorFilter: ColorFilter.mode(
                              context.colorScheme.mainPrimary, BlendMode.srcIn),
                          width: context.isTablet ? 20 : 20.w,
                          height: context.isTablet ? 20 : 20.w,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showModalBottomSheetExportMindMap(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: context.colorScheme.mainNeutral,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(20.r),
              ),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 24.h,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonText(
                      S.current.export_mind_map,
                      style: TextStyle(
                        fontSize: context.isTablet ? 22 : 20.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: SvgPicture.asset(
                        Assets.icons.icCloseWhite,
                        width: 24.w,
                        height: 24.h,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainGray,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ],
                ),
                AppConstants.kSpacingItem12,
                ValueListenableBuilder<Map<ExportFormat, bool>>(
                  valueListenable: loadingStates,
                  builder: (context, loadingMap, child) {
                    return Column(
                      children: [
                        ExportOptionItem(
                          text: ExportFormat.png.displayName,
                          isLoading: loadingMap[ExportFormat.png] ?? false,
                          onTap: () {
                            _handleExport(
                              context,
                              ExportFormat.png,
                              EventName.note_mindmap_export_png,
                            );
                          },
                        ),
                        AppConstants.kSpacingItem12,
                        ExportOptionItem(
                          text: ExportFormat.jpeg.displayName,
                          isLoading: loadingMap[ExportFormat.jpeg] ?? false,
                          onTap: () {
                            _handleExport(
                              context,
                              ExportFormat.jpeg,
                              EventName.note_mindmap_export_jpeg,
                            );
                          },
                        ),
                        AppConstants.kSpacingItem12,
                        ExportOptionItem(
                          text: ExportFormat.pdf.displayName,
                          isLoading: loadingMap[ExportFormat.pdf] ?? false,
                          onTap: () {
                            _handleExport(
                              context,
                              ExportFormat.pdf,
                              EventName.note_mindmap_export_pdf,
                            );
                          },
                        ),
                        AppConstants.kSpacingItem12,
                        ExportOptionItem(
                          text: ExportFormat.docx.displayName,
                          isLoading: loadingMap[ExportFormat.docx] ?? false,
                          onTap: () {
                            _handleExport(
                              context,
                              ExportFormat.docx,
                              EventName.note_mindmap_export_docx,
                            );
                          },
                        ),
                        AppConstants.kSpacingItem12,
                        ExportOptionItem(
                          text: ExportFormat.md.displayName,
                          isLoading: loadingMap[ExportFormat.md] ?? false,
                          onTap: () {
                            _handleExport(
                              context,
                              ExportFormat.md,
                              EventName.note_mindmap_export_md,
                            );
                          },
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleExport(
      BuildContext context, ExportFormat format, String eventName) async {
    loadingStates.value = {
      ...loadingStates.value,
      format: true,
    };

    final RenderBox box = context.findRenderObject() as RenderBox;
    final Rect rect = box.localToGlobal(Offset.zero) & box.size;

    try {
      final result = await widget.cubit.exportMindMap(format.displayFormat);
      loadingStates.value = {
        ...loadingStates.value,
        format: false,
      };
      if (result == null) return;
      try {
        if (mounted) {
          // ignore: use_build_context_synchronously
          Navigator.of(context).pop();
        }
        if (result.localPath != null) {
          await ShareService().shareFile(
            filePath: result.localPath!,
            sharePositionOrigin: rect,
            subject: widget.note.title,
            eventName: eventName,
            onSuccess: () {
              final InAppReview inAppReview = InAppReview.instance;
              inAppReview.requestReview();
            },
          );
          // Xóa file tạm sau khi share
          await File(result.localPath!).delete();
        } else {
          await ShareService().shareLink(
            link: result.url,
            sharePositionOrigin: rect,
            eventName: eventName,
          );
        }
      } catch (e) {
        await ShareService().shareLink(
          link: result.url,
          sharePositionOrigin: rect,
          eventName: eventName,
        );
      }
    } finally {
      loadingStates.value = {
        ...loadingStates.value,
        format: false,
      };
    }
  }
}

enum ExportFormat {
  png,
  jpeg,
  docx,
  pdf,
  md,
  ;

  String get displayFormat {
    switch (this) {
      case ExportFormat.png:
        return 'png';
      case ExportFormat.jpeg:
        return 'jpeg';
      case ExportFormat.docx:
        return 'docx';
      case ExportFormat.pdf:
        return 'pdf';
      case ExportFormat.md:
        return 'md';
    }
  }

  String get displayName {
    switch (this) {
      case ExportFormat.png:
        return S.current.image_png;
      case ExportFormat.jpeg:
        return S.current.image_jpeg;
      case ExportFormat.docx:
        return S.current.word_docx;
      case ExportFormat.pdf:
        return S.current.pdf_pdf;
      case ExportFormat.md:
        return S.current.markdown_md;
    }
  }
}
