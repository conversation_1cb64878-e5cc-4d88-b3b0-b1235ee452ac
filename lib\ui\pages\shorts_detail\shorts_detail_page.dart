import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/shorts_detail/short_details_state.dart';
import 'package:video_player/video_player.dart';

class ShortsDetailPage extends StatefulWidget {
  static const routeName = 'ShortsDetailPage';

  final int durationOption;
  final String voiceId;
  final bool isCaptionEnabled;
  final String noteId;
  final String title;
  final List<BackgroundVideoDto> listShortsBackground;
  final List<BackgroundQuizVideoDto> listShortsQuizBackground;
  final String selectedShortsUrl;
  final String videoId;
  final bool isQuizVideo;

  const ShortsDetailPage({
    super.key,
    this.durationOption = 60,
    this.voiceId = '',
    this.isCaptionEnabled = true,
    required this.title,
    this.noteId = '',
    this.listShortsBackground = const [],
    this.listShortsQuizBackground = const [],
    required this.selectedShortsUrl,
    this.videoId = '',
    this.isQuizVideo = false,
  });

  @override
  State<ShortsDetailPage> createState() => _ShortsDetailPage();
}

class _ShortsDetailPage
    extends BasePageStateDelegate<ShortsDetailPage, ShortsDetailCubit> {
  // UI element keys for sharing and downloading
  final GlobalKey _shareButtonKey = GlobalKey();
  final GlobalKey _downloadButtonKey = GlobalKey();

  @override
  void initState() {
    super.initState();

    // Initialize cubit with configuration for shorts only
    cubit.initData(
      widget.isCaptionEnabled,
      false,
      widget.isQuizVideo,
    );
    cubit.initVideoPlayer(widget.selectedShortsUrl);

    if (widget.isQuizVideo) {
      // Initialize selectedQuizVideoUrl with the first item's URL
      if (widget.listShortsQuizBackground.isNotEmpty) {
        cubit.updateSelectedQuizVideoUrl(
          widget.listShortsQuizBackground.first.videoUrl,
        );
      }
      cubit.generateShortsQuiz(
        widget.durationOption,
        widget.voiceId,
        widget.noteId,
        widget.videoId,
      );
    } else {
      cubit.generateShorts(
        widget.noteId,
        widget.durationOption,
        widget.voiceId,
        widget.selectedShortsUrl,
        widget.videoId,
      );
    }

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      cubit.titleNotifier.value = widget.title;
    });
  }

  @override
  void dispose() {
    // Clean up resources
    cubit.videoController?.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colorScheme.mainBackground,
      body: SafeArea(
        child: BlocListener<ShortsDetailCubit, ShortDetailsState>(
          listenWhen: (previous, current) =>
              widget.isQuizVideo &&
              previous.shortQuizOneShotEvent != current.shortQuizOneShotEvent &&
              current.shortQuizOneShotEvent == ShortQuizOneShotEvent.success,
          listener: (context, state) {
            // Initialize quiz video when API success
            if (state.quizVideoUrl.isNotEmpty) {
              _initializeQuizVideo(state.quizVideoUrl);
            }
          },
          child: Column(
            children: [
              _buildTopBar(),
              Expanded(
                child: _buildVideoPlayer(),
              ),
              AppConstants.kSpacingItem16,
              _buildBottomSection(),
            ],
          ),
        ),
      ),
    );
  }

  /// Initializes the quiz video player
  Future<void> _initializeQuizVideo(String quizVideoUrl) async {
    await cubit.initVideoPlayerDirectFromUrl(quizVideoUrl);
  }

  /// Builds the bottom section with controls and temporary content message
  Widget _buildBottomSection() {
    return BlocBuilder<ShortsDetailCubit, ShortDetailsState>(
      buildWhen: (previous, current) {
        if (widget.isQuizVideo) {
          return previous.shortQuizOneShotEvent !=
              current.shortQuizOneShotEvent;
        } else {
          return previous.processingStatus != current.processingStatus;
        }
      },
      builder: (context, state) {
        // For quiz videos, check ShortQuizOneShotEvent
        if (widget.isQuizVideo &&
            state.shortQuizOneShotEvent != ShortQuizOneShotEvent.success) {
          return const SizedBox.shrink();
        }

        // For regular shorts, check ProcessingStatus
        if (!widget.isQuizVideo &&
            state.processingStatus != ProcessingStatus.complete) {
          return const SizedBox.shrink();
        }

        return Column(
          children: [
            _buildBottomControls(),
            _buildTemporaryContentMessage(),
          ],
        );
      },
    );
  }

  /// Builds the temporary content message
  Widget _buildTemporaryContentMessage() {
    return ShortsDetailCommonWidgets.buildTemporaryContentMessage(
      context: context,
      isAudio: false,
      isTablet: cubit.appCubit.isTablet,
    );
  }

  /// Builds the video player for shorts mode
  Widget _buildVideoPlayer() {
    return BlocBuilder<ShortsDetailCubit, ShortDetailsState>(
      buildWhen: (previous, current) {
        return previous.processingStatus != current.processingStatus ||
            previous.currentPlayingFilePath != current.currentPlayingFilePath ||
            previous.shortQuizOneShotEvent != current.shortQuizOneShotEvent ||
            previous.progressMessage != current.progressMessage;
      },
      builder: (context, state) {
        // Handle error state
        if ((state.processingStatus == ProcessingStatus.error ||
                state.shortQuizOneShotEvent == ShortQuizOneShotEvent.error) &&
            (state.quizVideoUrl.isEmpty ||
                !(cubit.videoController?.value.isInitialized ?? false))) {
          return Center(child: CommonText(S.current.fail_to_load_video));
        }

        // For quiz videos in loading state, show background video with processing overlay
        if (widget.isQuizVideo) {
          if (state.shortQuizOneShotEvent == ShortQuizOneShotEvent.loading) {
            if (cubit.videoController?.value.isInitialized ?? false) {
              return _buildInitializedVideoPlayer(
                ShortsDetailCommonWidgets.buildProcessingOverlayShortQuiz(
                  context: context,
                  status: ShortQuizOneShotEvent.loading,
                  progressMessage: state.progressMessage,
                ),
              );
            } else {
              return _buildVideoLoadingPlaceholder();
            }
          } else if (state.shortQuizOneShotEvent ==
              ShortQuizOneShotEvent.success) {
            // If we have a success state but controller is not initialized,
            // try to force initialization from the stored URL
            if (!(cubit.videoController?.value.isInitialized ?? false) &&
                state.quizVideoUrl.isNotEmpty) {
              // Schedule initialization on the next frame
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _initializeQuizVideo(state.quizVideoUrl);
              });

              return _buildVideoLoadingPlaceholder();
            }
          }
        }

        // For regular shorts, handle processing overlay based on ProcessingStatus
        Widget? overlay;
        if (!widget.isQuizVideo &&
            state.processingStatus != ProcessingStatus.complete) {
          overlay = _buildProcessingOverlay(state.processingStatus);
        }

        // Build the appropriate content based on video initialization state
        if (cubit.videoController?.value.isInitialized ?? false) {
          return _buildInitializedVideoPlayer(overlay);
        } else {
          return _buildVideoLoadingPlaceholder();
        }
      },
    );
  }

  /// Builds the video player when the controller is initialized
  Widget _buildInitializedVideoPlayer(Widget? overlay) {
    final size = cubit.videoController!.value.size;
    final videoRatio = size.width / size.height;

    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Calculate dimensions to maintain aspect ratio
          final maxHeight = constraints.maxHeight;
          final maxWidth = constraints.maxWidth;

          double targetWidth = maxHeight * videoRatio;
          double targetHeight = maxHeight;

          if (targetWidth > maxWidth) {
            targetWidth = maxWidth;
            targetHeight = maxWidth / videoRatio;
          }

          return Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16.r),
              child: SizedBox(
                width: targetWidth,
                height: targetHeight,
                child: Stack(
                  children: [
                    // Video player
                    AspectRatio(
                      aspectRatio: videoRatio,
                      child: VideoPlayer(cubit.videoController!),
                    ),

                    // Play/pause controls (only when not showing overlay)
                    if (overlay == null) _buildVideoPlayPauseControl(),

                    // Video overlay controls (only when not showing overlay)
                    if (overlay == null)
                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: _buildVideoOverlayControls(),
                      ),

                    // Processing overlay if needed
                    if (overlay != null)
                      Positioned(
                        left: 0,
                        right: 0,
                        bottom: 50,
                        child: overlay,
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// Builds the play/pause control overlay for the video
  Widget _buildVideoPlayPauseControl() {
    return Positioned.fill(
      child: GestureDetector(
        onTap: _toggleVideoPlayback,
        child: Container(
          color: Colors.transparent,
          child: Center(
            child: ValueListenableBuilder(
              valueListenable: cubit.videoController!,
              builder: (context, VideoPlayerValue value, child) {
                return AnimatedOpacity(
                  opacity: value.isPlaying ? 0.0 : 1.0,
                  duration: const Duration(milliseconds: 300),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black45,
                      borderRadius: BorderRadius.circular(50.r),
                    ),
                    child: Icon(
                      value.isPlaying ? Icons.pause : Icons.play_arrow,
                      size: 50,
                      color: Colors.white,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  /// Toggle video playback between play and pause
  void _toggleVideoPlayback() {
    if (cubit.videoController?.value.isInitialized ?? false) {
      if (cubit.videoController!.value.isPlaying) {
        cubit.videoController!.pause();
      } else {
        cubit.videoController!.play();
      }
    }
  }

  /// Builds a placeholder while the video is loading
  Widget _buildVideoLoadingPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black12,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Center(
        child: CommonText(
          S.current.preparing_video,
          style: TextStyle(
            color: context.colorScheme.mainPrimary,
            fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  /// Builds the processing overlay with status message
  Widget _buildProcessingOverlay(ProcessingStatus status) {
    return ShortsDetailCommonWidgets.buildProcessingOverlay(
      context: context,
      status: status,
    );
  }

  /// Shows a confirmation dialog when user attempts to cancel/close
  void _handleOnCancelRecord() {
    ShortsDetailCommonWidgets.showCancelConfirmationDialog(context);
  }

  /// Builds the top app bar with title and close button
  Widget _buildTopBar() {
    return ShortsDetailCommonWidgets.buildTopBar(
      context: context,
      titleNotifier: cubit.titleNotifier,
      onCancelTap: _handleOnCancelRecord,
    );
  }

  /// Builds the bottom controls for sharing and exporting content
  Widget _buildBottomControls() {
    return ShortsDetailCommonWidgets.buildBottomControls(
      context: context,
      shareButtonKey: _shareButtonKey,
      downloadButtonKey: _downloadButtonKey,
      isAudio: false,
      isTablet: cubit.appCubit.isTablet,
      onDownloadOrShare: (context, {required buttonKey, required isDownload}) {
        cubit.downloadVideo(
          context,
          buttonKey: buttonKey,
          isDownload: isDownload,
          isShort: true,
        );
      },
    );
  }

  /// Builds the video overlay controls for changing background
  Widget _buildVideoOverlayControls() {
    return BlocBuilder<ShortsDetailCubit, ShortDetailsState>(
      buildWhen: (previous, current) =>
          previous.isWatermarkEnable != current.isWatermarkEnable ||
          previous.selectedUrlShortsBackgrounds !=
              current.selectedUrlShortsBackgrounds ||
          previous.selectedQuizVideoUrl != current.selectedQuizVideoUrl,
      builder: (context, state) {
        return Padding(
          padding: EdgeInsets.fromLTRB(16.w, 0.h, 0.w, 12.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const SizedBox.shrink(),
              //_buildBackgroundSelector(state),
              widget.isQuizVideo
                  ? _buildQuizVideoSelector(state)
                  : _buildBackgroundSelector(state),
            ],
          ),
        );
      },
    );
  }

  /// Builds the background selector dropdown
  Widget _buildBackgroundSelector(ShortDetailsState state) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2<String>(
        value: state.selectedUrlShortsBackgrounds,
        // Hide the default dropdown icon
        iconStyleData: const IconStyleData(
          icon: SizedBox.shrink(),
        ),
        // Style the dropdown menu
        dropdownStyleData: DropdownStyleData(
          decoration: BoxDecoration(
            color: context.colorScheme.mainSecondary,
            borderRadius: BorderRadius.circular(16.r),
          ),
          padding: EdgeInsets.zero,
          offset: Offset(cubit.appCubit.isTablet ? -8 : -8.w, 0),
          width: context.isTablet ? 200 : 200.w,
          maxHeight: 220.h,
          useSafeArea: true,
          direction: DropdownDirection.left,
        ),
        // Custom button appearance
        customButton: Padding(
          padding: EdgeInsets.all(cubit.appCubit.isTablet ? 8 : 8.w),
          child: SvgPicture.asset(
            Assets.icons.icShortsChangeBg,
            width: context.isTablet ? 40 : 40.w,
            height: context.isTablet ? 40 : 40.w,
          ),
        ),
        // Style for menu items
        menuItemStyleData: MenuItemStyleData(
          padding: EdgeInsets.zero,
          height: 44.h,
        ),
        buttonStyleData: ButtonStyleData(
          overlayColor: WidgetStateProperty.all(Colors.transparent),
        ),
        isExpanded: true,
        // Generate dropdown items from background list
        items: _buildBackgroundDropdownItems(state),
        // Handle selection change
        onChanged: (newValue) =>
            cubit.handleBackgroundChange(newValue, widget.listShortsBackground),
      ),
    );
  }

  /// Builds the list of dropdown items for background selection
  List<DropdownMenuItem<String>> _buildBackgroundDropdownItems(
      ShortDetailsState state) {
    return widget.listShortsBackground.map((item) {
      final isSelected = item.videoUrl == state.selectedUrlShortsBackgrounds;
      return DropdownMenuItem<String>(
        value: item.videoUrl,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            horizontal: 12.w,
            vertical: 10.h,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? context.colorScheme.mainNeutral
                : context.colorScheme.mainSecondary,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CommonText(
                  item.title,
                  style: TextStyle(
                    fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                    fontWeight: FontWeight.w500,
                    color: context.colorScheme.mainPrimary,
                  ),
                  maxLines: 1,
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check,
                  size: 24,
                  color: context.colorScheme.mainBlue,
                ),
            ],
          ),
        ),
      );
    }).toList();
  }

  /// Builds the quiz video selector dropdown
  Widget _buildQuizVideoSelector(ShortDetailsState state) {
    // If no quiz videos available, return empty widget
    if (widget.listShortsQuizBackground.isEmpty) {
      return const SizedBox.shrink();
    }

    return DropdownButtonHideUnderline(
      child: DropdownButton2<String>(
        value: state.selectedQuizVideoUrl,
        iconStyleData: const IconStyleData(
          icon: SizedBox.shrink(),
        ),
        dropdownStyleData: DropdownStyleData(
          decoration: BoxDecoration(
            color: context.colorScheme.mainSecondary,
            borderRadius: BorderRadius.circular(16.r),
          ),
          padding: EdgeInsets.zero,
          offset: Offset(cubit.appCubit.isTablet ? -8 : -8.w, 0),
          width: context.isTablet ? 200 : 200.w,
          maxHeight: 220.h,
          useSafeArea: true,
          direction: DropdownDirection.left,
        ),
        customButton: Padding(
          padding: EdgeInsets.all(cubit.appCubit.isTablet ? 8 : 8.w),
          child: SvgPicture.asset(
            Assets.icons.icShortsChangeBg,
            width: context.isTablet ? 40 : 40.w,
            height: context.isTablet ? 40 : 40.w,
          ),
        ),
        menuItemStyleData: MenuItemStyleData(
          padding: EdgeInsets.zero,
          height: 44.h,
        ),
        buttonStyleData: ButtonStyleData(
          overlayColor: WidgetStateProperty.all(Colors.transparent),
        ),
        isExpanded: true,
        items: _buildQuizVideoDropdownItems(state),
        onChanged: (newValue) {
          if (newValue != null) {
            cubit.handleQuizVideoChange(
              newValue,
              widget.listShortsQuizBackground,
              widget.durationOption,
              widget.voiceId,
              widget.noteId,
            );
          }
        },
      ),
    );
  }

  /// Builds the list of dropdown items for quiz video selection
  List<DropdownMenuItem<String>> _buildQuizVideoDropdownItems(
      ShortDetailsState state) {
    return widget.listShortsQuizBackground.map((item) {
      final isSelected = item.videoUrl == state.selectedQuizVideoUrl;
      return DropdownMenuItem<String>(
        value: item.videoUrl,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            horizontal: 12.w,
            vertical: 10.h,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? context.colorScheme.mainNeutral
                : context.colorScheme.mainSecondary,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CommonText(
                  item.title,
                  style: TextStyle(
                    fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                    fontWeight: FontWeight.w500,
                    color: context.colorScheme.mainPrimary,
                  ),
                  maxLines: 1,
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check,
                  size: 24,
                  color: context.colorScheme.mainBlue,
                ),
            ],
          ),
        ),
      );
    }).toList();
  }
}
