import 'package:get_it/get_it.dart';

import '../../../../base/base_cubit.dart';
import '../../../../base/local_service.dart';
import 'custom_tab_setting_state.dart';

class CustomTabSettingCubit extends BaseCubit<CustomTabSettingState> {
  final LocalService _localService = GetIt.instance.get<LocalService>();

  CustomTabSettingCubit() : super(const CustomTabSettingState());

  Future<void> initTabs(List<String> allTabs, List<String> alwaysSelected) async {
    // Load saved tabs from local storage
    final savedTabs = await _localService.loadSelectedItems();

    // Ensure always selected tabs are included
    final selectedTabs = [...savedTabs];
    for (final tab in alwaysSelected) {
      if (!selectedTabs.contains(tab)) {
        selectedTabs.add(tab);
      }
    }

    emit(state.copyWith(
      allTabs: allTabs,
      alwaysSelected: alwaysSelected,
      selectedTabs: selectedTabs,
      isSaveButtonEnabled: selectedTabs.length >= 4,
    ));
  }

  void toggleTab(String tab) {
    if (state.alwaysSelected.contains(tab)) return;

    final newSelectedTabs = List<String>.from(state.selectedTabs);
    if (newSelectedTabs.contains(tab)) {
      newSelectedTabs.remove(tab);
    } else {
      newSelectedTabs.add(tab);
    }

    emit(state.copyWith(
      selectedTabs: newSelectedTabs,
      isSaveButtonEnabled: newSelectedTabs.length >= 4,
    ));
  }

  void removeTab(String tab) {
    if (!state.alwaysSelected.contains(tab)) {
      final newSelectedTabs = List<String>.from(state.selectedTabs);
      newSelectedTabs.remove(tab);
      emit(state.copyWith(
        selectedTabs: newSelectedTabs,
        isSaveButtonEnabled: newSelectedTabs.length >= 4,
      ));
    }
  }

  void selectAll() {
    final newSelectedTabs = [
      ...state.alwaysSelected,
      ...state.allTabs.where((e) => !state.alwaysSelected.contains(e))
    ];
    emit(state.copyWith(
      selectedTabs: newSelectedTabs,
      isSaveButtonEnabled: true,
    ));
  }

  void deselectAll() {
    emit(state.copyWith(
      selectedTabs: [...state.alwaysSelected],
      isSaveButtonEnabled: false,
    ));
  }

  int getTabIndex(String tab) => state.selectedTabs.indexOf(tab) + 1;

  Future<void> saveSelectedTabs() async {
    await _localService.saveSelectedItems(state.selectedTabs);
  }
}
