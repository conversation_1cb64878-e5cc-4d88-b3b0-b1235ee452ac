import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:note_x/lib.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';
import '../../../base/base_page_state.dart';
import 'cubit/create_shorts_state.dart';

class CreateShortsPage extends StatefulWidget {
  final NoteModel noteModel;
  final CreateShortsType type;

  const CreateShortsPage({
    super.key,
    required this.noteModel,
    required this.type,
  });

  @override
  State<CreateShortsPage> createState() => _CreateShortsPageState();
}

class _CreateShortsPageState
    extends BasePageStateDelegate<CreateShortsPage, CreateShortsCubit>
    with SingleTickerProviderStateMixin {
  String? selectedVoiceId;
  bool? isCaptionSupported;

  final AudioPlayerController _audioController = AudioPlayerController();

  @override
  void initState() {
    super.initState();
    cubit.getAllBackgroundVideos();
    cubit.getAllBackgroundQuizVideos();
    cubit.updateSelectedDurationIndex(1);
  }

  @override
  void dispose() {
    _audioController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: context.colorScheme.mainNeutral,
        body: Container(
          decoration: BoxDecoration(
            color: context.colorScheme.mainNeutral,
          ),
          child: Stack(
            children: [
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonText(
                            S.current.settings,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: context.isTablet ? 22 : 20.sp,
                              color: context.colorScheme.mainPrimary,
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              cubit.onShortsBack(widget.type);
                              Navigator.pop(context);
                            },
                            child: SvgPicture.asset(
                              Assets.icons.icCloseWhite,
                              width: 24.w,
                              height: 24.h,
                              colorFilter: ColorFilter.mode(
                                context.colorScheme.mainGray,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    AppConstants.kSpacingItem16,
                    Expanded(
                      child: widget.type == CreateShortsType.audio
                          ? tabAudioWidget()
                          : tabVideoWidget(),
                    ),
                    AppConstants.kSpacingItem24,
                  ],
                ),
              ),

              /// button generate video
              Positioned(
                left: 16.w,
                right: 16.w,
                bottom: 24.h,
                child: BlocBuilder<CreateShortsCubit, CreateShortsState>(
                  builder: (context, state) {
                    return CommonButton(
                      isEnable: (state.selectedVideoIndex != -1 ||
                              state.selectedQuizVideoIndex != -1 ||
                              widget.type == CreateShortsType.audio) &&
                          widget.noteModel.voices.isNotEmpty,
                      radiusSize: 24.r,
                      minWidth: double.infinity,
                      height: 48.h,
                      backgroundColor: context.colorScheme.mainBlue,
                      backgroundColorDisable: context.colorScheme.mainSecondary,
                      btnText: widget.type == CreateShortsType.audio
                          ? S.current.generate_audio
                          : S.current.generate_video,
                      btnTextStyle: TextStyle(
                        fontSize: context.isTablet ? 18 : 16.sp,
                        fontWeight: FontWeight.w500,
                      ),
                      btnTextColorEnable: AppColors.white,
                      btnTextColorDisable: context.colorScheme.mainGray,
                      onPress: () async {
                        if (cubit.appCubit.getAppUser().shortsCredit > 0 ||
                            cubit.appCubit.getAppUser().rewardCredits > 0) {
                          _goToShortDetailPage(state.isCaptionEnabled == true &&
                              isCaptionSupported == true);
                          final status = await Permission.notification.status;
                          if (status.isDenied || status.isPermanentlyDenied) {
                            showBlackCupertinoDialog(
                              // ignore: use_build_context_synchronously
                              context: context,
                              title: S.current.noti_req_title,
                              message: S.current.noti_req_description,
                              cancelButton: S.current.donotallow,
                              confirmButton: S.current.allow,
                              confirmButtonTextColor:
                                  // ignore: use_build_context_synchronously
                                  context.colorScheme.mainBlue,
                              onCancel: () {},
                              onConfirm: () {
                                NotificationUtils.checkAndNavigateToSettings();
                              },
                            );
                            return;
                          }
                        } else {
                          if (cubit.appCubit.isUserFree()) {
                            Navigator.push(
                              context,
                              CupertinoPageRoute(
                                builder: (context) => const PurchasePage(
                                  from: PurchasePageFrom.iapCreateNote,
                                ),
                                fullscreenDialog: true,
                              ),
                            ).then((didPurchaseSuccess) {
                              if (didPurchaseSuccess == true) {
                                _goToShortDetailPage(
                                  state.isCaptionEnabled == true &&
                                      isCaptionSupported == true,
                                );
                              }
                            });
                          } else {
                            showBlackCupertinoDialog(
                                context: context,
                                title: S.current.daily_shorts_limit_reached,
                                confirmButton: S.current.ok,
                                confirmButtonTextColor:
                                    context.colorScheme.mainBlue,
                                message: S
                                    .current.daily_shorts_limit_reached_detail);
                          }
                        }
                      },
                    );
                  },
                ),
              ),

              Positioned(
                left: 16.w,
                right: 16.w,
                bottom: 0.h,
                child: BlocBuilder<CreateShortsCubit, CreateShortsState>(
                  builder: (context, state) {
                    return state.selectedQuizVideoIndex != -1
                        ? Center(
                            child: CommonText(
                              S.current.preview_only,
                              style: TextStyle(
                                fontSize: context.isTablet ? 12 : 10.sp,
                                fontWeight: FontWeight.w400,
                                color: context.colorScheme.mainGray,
                              ),
                              maxLines: 1,
                            ),
                          )
                        : const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _goToShortDetailPage(bool isCaptionEnabled) {
    // Stop audio playback from current screen
    _audioController.stopAudio();

    // Pause and stop video if playing
    if (cubit.videoController != null) {
      if (cubit.videoController!.value.isPlaying) {
        cubit.videoController!.pause();
      }
      // Set volume to 0 to ensure no audio leakage
      cubit.videoController!.setVolume(0);
    }

    final selectedItem = cubit.getCurrentSelectedItem();
    if (widget.type == CreateShortsType.audio) {
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.podcast_btmsht_create_clicked,
      );
      // Directly navigate to PodcastDetailPage for audio shorts
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PodcastDetailPage(
            title: selectedItem?.title ?? '',
            durationOption:
                widget.noteModel.clipDurations[cubit.selectedDurationIndex],
            noteId: widget.noteModel.backendNoteId,
            voiceId: selectedVoiceId ?? '',
          ),
          fullscreenDialog: true,
        ),
      );
    } else {
      if (selectedItem != null) {
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.shorts_btmsht_create_clicked,
        );
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ShortsDetailPage(
              title: selectedItem.title,
              selectedShortsUrl: selectedItem.videoUrl,
              durationOption:
                  widget.noteModel.clipDurations[cubit.selectedDurationIndex],
              noteId: widget.noteModel.backendNoteId,
              videoId: selectedItem.backgroundId,
              voiceId: selectedVoiceId ?? '',
              isCaptionEnabled: isCaptionEnabled,
              listShortsBackground: cubit.backgroundVideos,
              listShortsQuizBackground: cubit.backgroundQuizVideos,
              isQuizVideo: cubit.isSelectedVideoQuiz(),
            ),
            fullscreenDialog: true,
          ),
        );
      }
    }
  }

  Widget _durationAndVoiceWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          CommonText(
            S.current.duration,
            style: TextStyle(
              fontSize: context.isTablet ? 16 : 14.sp,
              fontWeight: FontWeight.w400,
              color: context.colorScheme.mainGray,
            ),
          ),
          AppConstants.kSpacingItem8,
          BlocBuilder<CreateShortsCubit, CreateShortsState>(
            buildWhen: (previous, current) =>
                previous.selectedDurationIndex != current.selectedDurationIndex,
            builder: (context, state) {
              if (widget.noteModel.clipDurations.isEmpty) {
                AnalyticsService.logAnalyticsEventNoParam(
                  eventName: EventName.shortsDurationIsEmpty,
                );
              }
              return SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: List.generate(
                    widget.noteModel.clipDurations.length,
                    (index) => GestureDetector(
                      onTap: () {
                        cubit.updateSelectedDurationIndex(index);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 24.w),
                        margin: EdgeInsets.only(
                          right:
                              index == widget.noteModel.clipDurations.length - 1
                                  ? 0
                                  : 12.w,
                        ),
                        height: 36.h,
                        decoration: BoxDecoration(
                          color: state.selectedDurationIndex == index
                              ? context.colorScheme.mainPrimary
                              : context.colorScheme.mainSecondary,
                          borderRadius: BorderRadius.circular(24.r),
                        ),
                        child: Center(
                          child: CommonText(
                            '${widget.noteModel.clipDurations[index]}s',
                            style: TextStyle(
                              color: state.selectedDurationIndex == index
                                  ? context.colorScheme.mainSecondary
                                  : context.colorScheme.mainPrimary,
                              fontSize: context.isTablet ? 16 : 14.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
          AppConstants.kSpacingItem16,
          CommonText(
            S.current.voice,
            style: TextStyle(
              fontSize: context.isTablet ? 16 : 14.sp,
              fontWeight: FontWeight.w400,
              color: context.colorScheme.mainGray,
            ),
          ),
          AppConstants.kSpacingItem8,

          /// UI VOICE
          VoiceDropdownWidget(
            noteModel: widget.noteModel,
            audioController: _audioController,
            onVoiceSelected: (VoiceModel voiceModel) {
              selectedVoiceId = voiceModel.voiceId;
              isCaptionSupported = voiceModel.isCaptionSupport;
            },
          ),
        ],
      ),
    );
  }

  Widget tabAudioWidget() {
    return Column(
      children: [
        _durationAndVoiceWidget(),
      ],
    );
  }

  Widget tabVideoWidget() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _durationAndVoiceWidget(),
        AppConstants.kSpacingItem25,

        /// Start Video caption

        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              (isCaptionSupported == false)
                  ? const SizedBox()
                  : BlocBuilder<CreateShortsCubit, CreateShortsState>(
                      builder: (context, state) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CommonText(
                              S.current.video_captions,
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: context.isTablet ? 16 : 14.sp,
                                color: state.selectedQuizVideoIndex != -1
                                    ? context.colorScheme.mainGray
                                        .withOpacity(0.38)
                                    : context.colorScheme.mainGray,
                              ),
                            ),
                            CupertinoSwitch(
                              activeColor: context.colorScheme.mainBlue,
                              value: (isCaptionSupported == true)
                                  ? !cubit.isSelectedVideoQuiz() &&
                                      state.isCaptionEnabled
                                  : false,
                              onChanged: (isCaptionSupported == true)
                                  ? (value) {
                                      cubit.toggleValueVideoCaption(value);
                                    }
                                  : null,
                              trackColor: state.selectedQuizVideoIndex != -1
                                  ? context.colorScheme.mainBackground
                                      .withOpacity(0.38)
                                  : context.colorScheme.mainGray,
                              thumbColor: state.selectedQuizVideoIndex != -1
                                  ? context.colorScheme.mainPrimary
                                      .withOpacity(0.38)
                                  : Colors.white,
                            ),
                          ],
                        );
                      },
                    ),
              AppConstants.kSpacingItem25,

              /// End Video caption
              CommonText(
                S.current.style,
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: context.isTablet ? 16 : 14.sp,
                  color: context.colorScheme.mainGray,
                ),
              ),
              AppConstants.kSpacingItem8,
            ],
          ),
        ),

        /// Start GridView for video thumbnails
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: BlocBuilder<CreateShortsCubit, CreateShortsState>(
              builder: (context, state) {
                return GridView.builder(
                  padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).padding.bottom + 60.h,
                  ),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 9 / 16,
                    mainAxisSpacing: 12,
                    crossAxisSpacing: 12,
                  ),
                  itemCount: state.listBackgroundVideos.length +
                      state.listBackgroundQuizVideos.length,
                  itemBuilder: (context, index) {
                    if (index < state.listBackgroundQuizVideos.length) {
                      // Show quiz videos first
                      return itemBackGroundStyle(
                        index,
                        state.listBackgroundQuizVideos,
                        isQuizVideo: true,
                      );
                    } else {
                      // Show regular videos after quiz videos
                      final regularIndex =
                          index - state.listBackgroundQuizVideos.length;
                      return itemBackGroundStyle(
                        regularIndex,
                        state.listBackgroundVideos,
                        isQuizVideo: false,
                      );
                    }
                  },
                );
              },
            ),
          ),
        ),

        /// End GridView for video thumbnails
      ],
    );
  }

  Widget itemBackGroundStyle(
    int index,
    List<dynamic> listBackgroundVideos, {
    bool isQuizVideo = false,
  }) {
    final item = listBackgroundVideos[index];
    final title = item is BackgroundVideoDto
        ? item.title
        : (item as BackgroundQuizVideoDto).title;
    final thumbnailUrl = item is BackgroundVideoDto
        ? item.thumbnailUrl
        : (item as BackgroundQuizVideoDto).thumbnailUrl;

    return BlocBuilder<CreateShortsCubit, CreateShortsState>(
      buildWhen: (previous, current) =>
          (isQuizVideo
              ? previous.selectedQuizVideoIndex !=
                  current.selectedQuizVideoIndex
              : previous.selectedVideoIndex != current.selectedVideoIndex) ||
          previous.isPlaying != current.isPlaying ||
          previous.isLoading != current.isLoading,
      builder: (context, state) {
        final isSelected = isQuizVideo
            ? state.selectedQuizVideoIndex == index
            : state.selectedVideoIndex == index;
        return GestureDetector(
          onTap: () {
            if (isSelected) {
              // Toggle play/pause when tapping selected item
              cubit.togglePlayPause();
            } else {
              // Select new item and initialize video
              cubit.updateSelectedIndex(index, isQuizVideo);
            }
          },
          child: Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: isSelected
                        ? context.colorScheme.mainBlue
                        : Colors.transparent,
                    width: 3,
                  ),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12.r),
                  child: isSelected &&
                          cubit.videoController?.value.isInitialized == true
                      ? AspectRatio(
                          aspectRatio: cubit.videoController!.value.aspectRatio,
                          child: VideoPlayer(cubit.videoController!),
                        )
                      : Image.network(
                          thumbnailUrl,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                        ),
                ),
              ),
              if (isSelected)
                Positioned.fill(
                  child: Center(
                    child: state.isLoading
                        ? Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.black45,
                              borderRadius: BorderRadius.circular(30.r),
                            ),
                            child: const CircularProgressIndicator(
                              color: Colors.white,
                            ),
                          )
                        : cubit.videoController?.value.isInitialized == true
                            ? AnimatedOpacity(
                                opacity: state.isPlaying ? 0.0 : 1.0,
                                duration: const Duration(milliseconds: 300),
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.black45,
                                    borderRadius: BorderRadius.circular(30.r),
                                  ),
                                  child: Icon(
                                    state.isPlaying
                                        ? Icons.pause
                                        : Icons.play_arrow,
                                    size: 30,
                                    color: Colors.white,
                                  ),
                                ),
                              )
                            : const SizedBox.shrink(),
                  ),
                ),

              /// TITLE
              Positioned(
                left: 8,
                right: 8,
                bottom: 8,
                child: Center(
                  child: CommonText(
                    title,
                    maxLines: 1,
                    style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: context.isTablet ? 16 : 12.sp,
                        color: context.colorScheme.themeWhite),
                  ),
                ),
              ),

              /// Widget NEW
              Positioned(
                left: context.isTablet ? 160 : 66,
                right: 3,
                top: 3,
                child: isQuizVideo
                    ? Container(
                        width: 40,
                        height: 28,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: AppColors.gradient19,
                          ),
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(12.r),
                            bottomLeft: Radius.circular(16.r),
                          ),
                        ),
                        child: Stack(
                          children: [
                            Padding(
                              padding: EdgeInsets.only(left: 6.w, top: 2.w),
                              child: SvgPicture.asset(
                                Assets.icons.icAiGen,
                                width: 12,
                                height: 12,
                                colorFilter: ColorFilter.mode(
                                  context.colorScheme.themeWhite,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(left: 8.w),
                              child: Center(
                                child: CommonText(
                                  S.current.new_new,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w700,
                                    fontSize: context.isTablet ? 10 : 8.sp,
                                    color: context.colorScheme.themeWhite,
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
            ],
          ),
        );
      },
    );
  }
}
