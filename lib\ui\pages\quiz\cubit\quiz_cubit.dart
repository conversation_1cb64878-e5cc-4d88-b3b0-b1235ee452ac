import 'package:note_x/base/base.dart';
import 'package:note_x/ui/pages/quiz/cubit/quiz_state.dart';

import '../../../model/quiz/quiz_model_view.dart';

class QuizCubit extends BaseCubit<QuizState> {
  QuizCubit(super.initialState);

  List<QuizModelView> _quizzes = [];

  List<QuizModelView> get quizzes => _quizzes;

  void init(List<QuizModelView> quizzes) {
    _quizzes = quizzes;
    emit(state.copyWith(quizzes: quizzes));
  }

  void onChooseAnswer(
      {required int quizIndex, required int answerChosenIndex}) {
    final updatedQuiz = _quizzes[quizIndex].copyWith(
      answerChosenIndex: answerChosenIndex,
      isAnswered: true,
    );
    _quizzes[quizIndex] = updatedQuiz;

    emit(state.copyWith(
      quizzes: _quizzes,
      chosenQuiz: updatedQuiz,
    ));

    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_quizzes_choose_answer,
    );
  }

  void onNextQuiz() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_quizzes_next,
    );
  }
}
