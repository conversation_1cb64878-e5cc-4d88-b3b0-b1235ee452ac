import 'dart:async';
import 'package:el_tooltip/el_tooltip.dart';
import 'package:flutter/cupertino.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'cubit/record_app_state.dart';
import 'package:get_it/get_it.dart';

class RecordAppPage extends StatefulWidget {
  static const routeName = 'RecordAppPage';
  final bool isAutoStart;
  final RecordPageFrom from;

  const RecordAppPage(
      {super.key, this.isAutoStart = false, this.from = RecordPageFrom.home});

  @override
  State<RecordAppPage> createState() => _RecordAppState();
}

class _RecordAppState
    extends BasePageStateDelegate<RecordAppPage, RecordAppCubit> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    cubit.initRecord();
    cubit.listFolder = [
      FolderModel(
          id: 'all_notes', folderName: S.current.all_note, backendId: ''),
      ...HiveFolderService.getAllFolders()
    ];
    WidgetsBinding.instance.addPostFrameCallback((_) {
      cubit.initTooltip();
      _handleAutoStart();
    });
    AnalyticsService.logEventScreenView(
      screenClass: EventScreenClass.recordAudioPage,
      screenName: EventScreenName.record_audio,
    );
  }

  @override
  void dispose() {
    cubit.closeAudioPlayer();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<RecordAppCubit, RecordAppState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent &&
          current.oneShotEvent != RecordOneShotEvent.none,
      listener: (context, state) {
        _handleRecordEvents(context, state);
        cubit.resetEnumState();
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<RecordAppCubit, RecordAppState>(
      buildWhen: (previous, current) =>
          previous.isRecording != current.isRecording,
      builder: (context, state) {
        return _buildRecordAudioScreen();
      },
    );
  }

  Widget _buildRecordAudioScreen() {
    debugPrint('buildRecordAudioScreen');
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBarWidget(
        title: S.current.record_audio,
        centerTitle: true,
        onPressed: () => _handleBack(),
      ),
      body: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          behavior: HitTestBehavior.opaque,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: cubit.appCubit.isTablet
                        ? const EdgeInsets.all(36)
                        : EdgeInsets.all(16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonText(
                          S.current.folder,
                          textColor: context.colorScheme.mainGray,
                          style: TextStyle(
                            fontSize: context.isTablet ? 16 : 14.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        AppConstants.kSpacingItem8,
                        BlocBuilder<RecordAppCubit, RecordAppState>(
                          buildWhen: (previous, current) =>
                              previous.isRecording != current.isRecording,
                          builder: (context, state) => SizedBox(
                            child: FolderDropdownView(
                              folders: cubit.listFolder,
                              selectedFolder: cubit.chooseFolderName,
                              onMenuStateChanged: (isShowing) {
                                if (isShowing) {}
                              },
                              from: FolderDropdownFrom.recording,
                            ),
                          ),
                        ),
                        AppConstants.kSpacingItem16,
                        Row(
                          children: [
                            CommonText(
                              S.current.speech_language,
                              textColor: context.colorScheme.mainGray,
                              style: TextStyle(
                                fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            AppConstants.kSpacingItemW4,
                            ElTooltip(
                              padding: EdgeInsets.all(8.w),
                              content: Container(
                                constraints: BoxConstraints(
                                  maxWidth: 250.w,
                                  maxHeight: 115.h,
                                ),
                                child: const LanguageTipsWidget(
                                  isRecording: true,
                                  isYoutube: false,
                                  isCheckDocAndText: false,
                                ),
                              ),
                              position: ElTooltipPosition.bottomCenter,
                              color: context.colorScheme.mainBlue,
                              radius: Radius.circular(16.r),
                              child: SvgPicture.asset(
                                Assets.icons.icCommonInfoTooltip,
                                height: cubit.appCubit.isTablet ? 18 : 18.w,
                                width: cubit.appCubit.isTablet ? 18 : 18.w,
                                colorFilter: ColorFilter.mode(
                                  context.colorScheme.mainPrimary,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ],
                        ),
                        AppConstants.kSpacingItem8,
                        Column(
                          children: [
                            SizedBox(
                              width: double.infinity,
                              child: LanguageDropdownView(
                                onChanged: (lang) => cubit.setLanguage(lang),
                                onMenuStateChanged: (isShowing) {
                                  if (isShowing) {}
                                },
                                tooltipController: cubit.tooltipController,
                                useCase:
                                    LanguageDropdownUseCase.recordAndAudioFile,
                                from: LanguageDropdownFrom.record,
                              ),
                            ),
                          ],
                        ),
                        AppConstants.kSpacingItem16,
                        AdvancedWidget(
                          onSummaryStyleChanged: cubit.updateSummaryStyle,
                          onWritingStyleChanged: cubit.updateWritingStyle,
                          onAdditionalInstructionsChanged:
                              cubit.updateAdditionalInstructions,
                          onAdvancedToggled: cubit.setAdvancedEnabled,
                        )
                      ],
                    ),
                  ),
                ),
              ),
              BlocBuilder<RecordAppCubit, RecordAppState>(
                buildWhen: (previous, current) =>
                    previous.isRecording != current.isRecording,
                builder: (context, state) => Container(
                  padding: EdgeInsets.only(
                    bottom: context.isTablet ? 32 : 32.h,
                    top: 16,
                  ),
                  child: state.isRecording
                      ? Column(
                          children: [
                            _buildWaveformWidget(),
                            _buildRecordControllerWidget()
                          ],
                        )
                      : _buildButtonStartRecording(),
                ),
              ),
            ],
          )),
    );
  }

  void _handleBack() {
    cubit.getDurationAudio().value > 0 || cubit.isRecordPausing()
        ? showNewCupertinoDialog(
            context: context,
            title: S.current.discard_changes,
            message: S.current.content_discard_changes,
            image: Assets.icons.icDiscardChanges,
            cancelButton: S.current.cancel,
            confirmButton: S.current.discard,
            onCancel: () {},
            onConfirm: () => cubit.stopRecord(false),
          )
        : {Navigator.pop(context), cubit.onRecordBack()};
  }

  void _handleOnCancelRecord() {
    showNewCupertinoDialog(
      context: context,
      title: S.current.discard_changes,
      message: S.current.content_discard_changes,
      image: Assets.icons.icDiscardChanges,
      cancelButton: S.current.cancel,
      confirmButton: S.current.discard,
      onCancel: () {},
      onConfirm: () => cubit.stopRecord(false),
    );
  }

  Widget _buildButtonStartRecording() {
    return Container(
      alignment: Alignment.center,
      margin: EdgeInsets.symmetric(
        horizontal: context.isLandscape
            ? 360
            : cubit.appCubit.isTablet
                ? 240
                : 24.w,
      ),
      child: AppCommonButton(
          backgroundColor: AppColors.primaryRed,
          width: 163.w,
          borderRadius: BorderRadius.circular(24.r),
          height: cubit.appCubit.isTablet ? 44 : 44.h,
          textWidget: CommonText(
            S.current.start_record,
            style: TextStyle(
              fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
              fontWeight:
                  cubit.appCubit.isTablet ? FontWeight.w600 : FontWeight.w500,
              color: context.colorScheme.themeWhite,
            ),
          ),
          onPressed: () {
            _record();
          }),
    );
  }

  void _record() async {
    final hasPermission = await cubit.getRecordController().hasPermission();
    if (hasPermission) {
      await cubit.record();
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.record_audio_start,
      );
    } else {
      AnalyticsService.logAnalyticsEventNoParam(
          eventName: 'record_page_show_dialog_denied');

      showBlackCupertinoDialog(
        // ignore: use_build_context_synchronously
        context: context,
        title: S.current.recording_permission_denied,
        message: S.current.recording_permission_denied_details,
        confirmButton: S.current.setting,
        onConfirm: () => openAppSettings(),
        // ignore: use_build_context_synchronously
        confirmButtonTextColor: context.colorScheme.mainBlue,
      );
    }
  }

  Widget _buildWaveformWidget() {
    return Container(
      width: double.infinity,
      height: 120,
      margin: EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(child: AnimatedWaveList(stream: cubit.getAmplitudeSub())),
          cubit.appCubit.isTablet
              ? AppConstants.kSpacingItem24
              : AppConstants.kSpacingItem16,
          ValueListenableBuilder(
            valueListenable: cubit.getDurationAudio(),
            builder: (context, snapshot, child) {
              return CommonText(
                MyUtils.formatDurationFromSeconds(snapshot),
                style:
                    TextStyle(fontSize: cubit.appCubit.isTablet ? 18 : 16.sp),
                appFontWeight: AppFontWeight.semiBold,
                textColor: context.colorScheme.mainGray,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildRecordControllerWidget() {
    return BlocBuilder<RecordAppCubit, RecordAppState>(
      buildWhen: (previous, current) =>
          previous.isPausing != current.isPausing ||
          previous.isRecording != current.isRecording,
      builder: (context, state) => Container(
        margin: EdgeInsets.symmetric(horizontal: 24.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                _handleOnCancelRecord();
                HapticFeedback.lightImpact();
                AnalyticsService.logAnalyticsEventNoParam(
                  eventName: EventName.record_audio_restart,
                );
              },
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.all(18.w),
                    child: SvgPicture.asset(
                      cubit.appCubit.isReverseView
                          ? Assets.icons.icFlipReset
                          : Assets.icons.icReset,
                      width: cubit.appCubit.isTablet ? 32 : 24.w,
                      height: cubit.appCubit.isTablet ? 32 : 24.h,
                      colorFilter: ColorFilter.mode(
                        context.colorScheme.mainPrimary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  CommonText(
                    S.current.reset,
                    style: TextStyle(
                      fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  )
                ],
              ),
            ),
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () => state.isPausing
                  ? cubit.onResumeRecord()
                  : cubit.onPauseRecord(),
              child: Container(
                margin: EdgeInsets.only(bottom: 20.h),
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder:
                      (Widget child, Animation<double> animation) {
                    return ScaleTransition(scale: animation, child: child);
                  },
                  child: state.isPausing
                      ? SvgPicture.asset(
                          Assets.icons.icCreateNoteRecord,
                          key: const ValueKey('play'),
                          width: cubit.appCubit.isTablet ? 60 : 56.w,
                          height: cubit.appCubit.isTablet ? 60 : 56.h,
                        )
                      : SvgPicture.asset(
                          Assets.icons.icCreateNotePause,
                          key: const ValueKey('pause'),
                          width: cubit.appCubit.isTablet ? 60 : 56.w,
                          height: cubit.appCubit.isTablet ? 60 : 56.h,
                        ),
                ),
              ),
            ),
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                _handleStopAndSubmitRecord();
              },
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.all(18.w),
                    child: SvgPicture.asset(
                      cubit.appCubit.isReverseView
                          ? Assets.icons.icFlipCreateNoteDone
                          : Assets.icons.icCreateNoteDone,
                      width: cubit.appCubit.isTablet ? 32 : 24.w,
                      height: cubit.appCubit.isTablet ? 32 : 24.h,
                      colorFilter: ColorFilter.mode(
                        context.colorScheme.mainPrimary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  CommonText(
                    S.current.done_button_label,
                    style: TextStyle(
                      fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  void _handleAutoStart() {
    if (widget.isAutoStart) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _record();
        }
      });
    }
  }

  void _handleStopAndSubmitRecord() {
    if (cubit.targetLanguage == const Language('err', 'Not found!')) {
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted) {
          showNewCupertinoDialog(
            context: context,
            title: S.current.create_select_a_language,
            message: S.current.please_select_a_language,
            image: Assets.icons.icSelectLanguage,
            confirmButton: S.current.ok,
            onConfirm: () {},
          );
        }
      });
    } else {
      cubit.stopRecord(true);
      HapticFeedback.lightImpact();
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.record_audio_done,
      );
    }
  }

  void _handleRecordEvents(BuildContext context, RecordAppState state) async {
    switch (state.oneShotEvent) {
      case RecordOneShotEvent.none:
        break;
      case RecordOneShotEvent.createNoteSuccessfully:
        Navigator.of(context).pop();
        final savedTabs =
            await GetIt.instance.get<LocalService>().loadSelectedItems();
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => MyNoteDetailPage(
              noteModel: cubit.getNote(),
              isTablet: cubit.appCubit.isTablet,
              from: NoteDetailPageFrom.recordAudioScreen,
              savedTabs: savedTabs,
            ),
          ),
        );
        break;
      case RecordOneShotEvent.onShowIAP:
        Navigator.of(context).pushNamed(
          PurchasePage.routeName,
          arguments: {
            EventKey.from: PurchasePageFrom.iapRecordScreen,
          },
        ).then((didPurchaseSuccess) => {
              if (didPurchaseSuccess == true)
                {cubit.onSubmitNote()}
              else
                {cubit.saveLocalRecording()}
            });
        break;
      case RecordOneShotEvent.onShowLifetimeProIAP:
        Navigator.of(context)
            .push(
              MaterialPageRoute(
                builder: (context) => const PurchaseLifeTimePageOldVer(
                  from: PurchaseLifetimeFrom.recordPage,
                ),
                fullscreenDialog: true,
              ),
            )
            .then((didPurchaseSuccess) => {
                  if (didPurchaseSuccess == true)
                    {cubit.onSubmitNote()}
                  else
                    {cubit.saveLocalRecording()}
                });
        break;
    }
  }
}

enum RecordPageFrom { home, welcome }
