import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/home/<USER>/home_overlay_manager.dart';

typedef FormatCallbackHome = void Function(ExportType type);

class HomeOverFlowMenu extends StatefulWidget {
  final NoteModel note;
  final bool isCommunityNote;
  final Function(BuildContext) onShowFolder;
  final FormatCallbackHome onShowBottomSheet;
  final FocusNode focusNode;
  final Func0 onShowSharingView;
  final VoidCallback onDeleteNote;
  final bool isLastItem;
  final bool isInBottomHalf;
  final ValueGetter<bool>? calculateIsInBottomHalf;

  const HomeOverFlowMenu({
    super.key,
    required this.note,
    required this.isCommunityNote,
    required this.onShowFolder,
    required this.onShowBottomSheet,
    required this.focusNode,
    required this.onShowSharingView,
    required this.onDeleteNote,
    required this.isLastItem,
    this.isInBottomHalf = false,
    this.calculateIsInBottomHalf,
  });

  @override
  State<HomeOverFlowMenu> createState() => _HomeOverFlowMenuState();
}

class _HomeOverFlowMenuState extends State<HomeOverFlowMenu>
    with SingleTickerProviderStateMixin {
  OverlayEntry? _menuOverlay;
  OverlayEntry? _subMenuOverlay;
  Offset _menuPosition = Offset.zero;
  late AnimationController _submenuAnimationController;
  final menuKey = GlobalKey();
  bool _showMenuAbove = false;

  // Get reference to the overlay manager
  final _overlayManager = HomeOverlayManager();

  Offset _calculateIconCenterPosition() {
    final RenderBox? renderBox =
        menuKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return Offset.zero;

    final Offset position = renderBox.localToGlobal(Offset.zero);
    return Offset(position.dx + (renderBox.size.width / 2),
        position.dy + (renderBox.size.height / 2));
  }

  @override
  void initState() {
    super.initState();
    _submenuAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  @override
  void dispose() {
    _removeAll();
    _submenuAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: menuKey,
      onTapDown: (details) {
        _menuPosition = _calculateIconCenterPosition();
        _showMenu(context, widget.isLastItem);
      },
      child: SizedBox(
        width: context.isTablet
            ? MenuWidget.kIconContainerWidth
            : MenuWidget.kIconContainerWidth.w,
        height: context.isTablet
            ? MenuWidget.kIconContainerHeight
            : MenuWidget.kIconContainerHeight.h,
        child: Center(
          child: SvgPicture.asset(
            Assets.icons.icMore,
            width: context.isTablet
                ? MenuWidget.kIconWidth
                : MenuWidget.kIconWidth.w,
            height: context.isTablet
                ? MenuWidget.kIconHeight
                : MenuWidget.kIconHeight.w,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }

  void _showMenu(BuildContext context, bool isLastItem) {
    _removeAll(); // Just remove everything first

    final overlay = Overlay.of(context);

    // Calculate position in real-time if callback is provided
    if (widget.calculateIsInBottomHalf != null) {
      _showMenuAbove = widget.calculateIsInBottomHalf!();
    } else {
      _showMenuAbove = widget.isInBottomHalf;
    }

    _menuOverlay = OverlayEntry(
      builder: (_) => MenuWidget(
        position: _menuPosition,
        items: _buildMainMenuItems(context),
        onDismiss: _removeAll,
        onSubMenuRequested: _showSubMenu,
        isLastItem: isLastItem,
        showMenuAbove: _showMenuAbove,
      ),
    );

    overlay.insert(_menuOverlay!);
    _overlayManager.registerOverlay(
      menuOverlay: _menuOverlay,
      subMenuOverlay: null,
      removeCallback: _removeAll,
    );
  }

  List<HomeMenuItem> _buildMainMenuItems(BuildContext context) {
    final isNoteFailed = widget.note.noteStatus == NoteStatus.error;
    return [
      HomeMenuItem(
        isShowIconLeft: true,
        title: S.current.export,
        subItems: [
          HomeMenuItem(
            isShowIconLeft: true,
            title: S.current.export,
            onTap: () {
              _removeAll();
            },
            urlSvg: Assets.icons.icExport01,
          ),
          HomeMenuItem(
            title: S.current.summary,
            onTap: () {
              _removeAll();
              widget.onShowBottomSheet(ExportType.summary);
            },
          ),
          HomeMenuItem(
            title: S.current.transcript,
            onTap: () {
              _removeAll();
              widget.onShowBottomSheet(ExportType.transcript);
            },
          ),
          if (widget.note.flashcardSetDataModel?.setId.isNotEmpty == true) ...[
            HomeMenuItem(
              title: S.current.flashcard,
              onTap: () {
                _removeAll();
                widget.onShowBottomSheet(ExportType.flashcards);
                AnalyticsService.logAnalyticsEventNoParam(
                  eventName: EventName.note_detail_export_flashcard,
                );
              },
            ),
          ],
          if (widget.note.quizSetDataModel?.setId.isNotEmpty == true) ...[
            HomeMenuItem(
              title: S.current.quiz,
              onTap: () {
                _removeAll();
                widget.onShowBottomSheet(ExportType.quiz);
                AnalyticsService.logAnalyticsEventNoParam(
                  eventName: EventName.note_detail_export_quiz,
                );
              },
            ),
          ],
        ],
        urlSvg: Assets.icons.icExport01,
      ),
      HomeMenuItem(
        title: S.current.add_folder,
        onTap: () {
          _removeAll();
          widget.onShowFolder(context);
          AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.moreOptionOnHomeMoveToFolder,
          );
        },
        urlSvg: Assets.icons.icAdd,
      ),
      if (!widget.isCommunityNote && !isNoteFailed)
        HomeMenuItem(
          title: S.current.share_note_link,
          onTap: () {
            _removeAll();
            widget.onShowSharingView();
          },
          urlSvg: Assets.icons.icShareNote,
        ),
      if (!widget.isCommunityNote)
        HomeMenuItem(
          title: S.current.delete_note_item,
          onTap: () {
            AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.moreOptionOnHomeDeleteNoteShow,
            );
            _removeAll();
            showNewCupertinoDialog(
              context: context,
              title: S.current.delete_note,
              message: S.current.content_delete_note,
              image: Assets.icons.icMascottDelete,
              cancelButton: S.current.cancel,
              confirmButton: S.current.delete,
              onConfirm: () {
                widget.onDeleteNote();
                AnalyticsService.logAnalyticsEventNoParam(
                  eventName: EventName.moreOptionOnHomeDeleteNote,
                );
              },
            );
          },
          urlSvg: Assets.icons.icDelete,
        ),
    ];
  }

  void _showSubMenu(
      BuildContext context, Offset parentPosition, List<HomeMenuItem> items) {
    if (_subMenuOverlay != null) return;

    final overlay = Overlay.of(context);
    final Offset iconCenterPosition = _calculateIconCenterPosition();
    final double submenuLeft = iconCenterPosition.dx;
    final double submenuTop = _showMenuAbove
        ? iconCenterPosition.dy -
            MenuWidget.kMenuSpacing -
            (items.length * MenuWidget.kMenuItemHeight)
        : iconCenterPosition.dy + MenuWidget.kMenuSpacing;

    _submenuAnimationController.forward(from: 0);
    _menuOverlay?.remove();
    _menuOverlay = null;

    _subMenuOverlay = OverlayEntry(
      builder: (_) => FadeTransition(
        opacity: _submenuAnimationController,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.2, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _submenuAnimationController,
            curve: Curves.easeOut,
          )),
          child: MenuWidget(
            position:
                Offset(submenuLeft + MenuWidget.kSafeAreaPadding, submenuTop),
            items: items,
            onDismiss: _removeAll,
            onSubMenuRequested: _showSubMenu,
            isLastItem: widget.isLastItem,
            showMenuAbove: _showMenuAbove,
          ),
        ),
      ),
    );

    overlay.insert(_subMenuOverlay!);
  }

  void _removeAll() {
    _menuOverlay?.remove();
    _menuOverlay = null;
    _subMenuOverlay?.remove();
    _subMenuOverlay = null;
    _overlayManager.unregisterOverlay(_removeAll);
  }
}
