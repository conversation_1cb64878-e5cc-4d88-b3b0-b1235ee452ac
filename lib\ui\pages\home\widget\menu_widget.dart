import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class MenuWidget extends StatelessWidget {
  // Constants for menu dimensions and positioning
  static const double kMenuWidth = 200.0;
  static const double kMenuItemHeight = 48.0;
  static const double kMenuSpacing = 12.0; // Spacing between menu and trigger
  static const double kSafeAreaPadding = 8.0; // Padding from screen edges

  // Constants for the three-dot icon dimensions
  static const double kIconContainerWidth = 40.0;
  static const double kIconContainerHeight = 40.0;
  static const double kIconWidth = 24.0;
  static const double kIconHeight = 24.0;

  final Offset position;
  final List<HomeMenuItem> items;
  final VoidCallback onDismiss;
  final void Function(BuildContext, Offset, List<HomeMenuItem>)
      onSubMenuRequested;
  final bool isLastItem;
  final bool showMenuAbove;

  const MenuWidget({
    Key? key,
    required this.position,
    required this.items,
    required this.onDismiss,
    required this.onSubMenuRequested,
    required this.isLastItem,
    this.showMenuAbove = false,
  }) : super(key: key);

  double _calculateMenuHorizontalPosition(BuildContext context) {
    // Get screen width
    final double screenWidth = MediaQuery.of(context).size.width;
    final double safeAreaLeft = MediaQuery.of(context).padding.left;
    final double safeAreaRight = MediaQuery.of(context).padding.right;

    // Default position (to the left of the three-dot icon)
    double leftPosition = position.dx - (kMenuWidth - kIconContainerWidth / 2);

    // If showing above, align the menu to be centered with the three-dot icon
    if (showMenuAbove) {
      // Center the menu with the icon
      leftPosition = position.dx - (kMenuWidth / 2) + (kIconContainerWidth / 2);
    }

    // Ensure menu doesn't go off screen to the left
    if (leftPosition < safeAreaLeft) {
      leftPosition = safeAreaLeft + kSafeAreaPadding;
    }

    // Ensure menu doesn't go off screen to the right
    if (leftPosition + kMenuWidth > screenWidth - safeAreaRight) {
      leftPosition =
          screenWidth - safeAreaRight - kMenuWidth - kSafeAreaPadding;
    }

    return leftPosition;
  }

  double _calculateMenuPosition(BuildContext context) {
    // Get the menu height based on the number of items
    final double menuHeight = items.length * kMenuItemHeight;

    // Get screen dimensions
    final double screenHeight = MediaQuery.of(context).size.height;
    final double safeAreaTop = MediaQuery.of(context).padding.top;
    final double safeAreaBottom = MediaQuery.of(context).padding.bottom;

    // Calculate available space above and below the tap position
    final double spaceAbove = position.dy - safeAreaTop;
    final double spaceBelow = screenHeight - position.dy - safeAreaBottom;

    // Determine if we should show the menu above or below based on available space
    bool shouldShowAbove = showMenuAbove;

    // Override based on available space if needed
    if (spaceBelow < menuHeight + kMenuSpacing &&
        spaceAbove > menuHeight + kMenuSpacing) {
      // Not enough space below but enough space above
      shouldShowAbove = true;
    } else if (spaceAbove < menuHeight + kMenuSpacing &&
        spaceBelow > menuHeight + kMenuSpacing) {
      // Not enough space above but enough space below
      shouldShowAbove = false;
    } else if (spaceAbove > spaceBelow) {
      // If both are insufficient but more space above
      shouldShowAbove = true;
    }

    // Special case for last item
    if (isLastItem && !shouldShowAbove) {
      // For the last item, position the menu above the item but with enough space
      // to not overlap with the previous item
      return position.dy - kIconContainerHeight * 2;
    }

    // Calculate final position
    if (shouldShowAbove) {
      // Show menu above the three-dot icon, but leave space for the icon
      // Position the menu so it's above the icon with some spacing
      double topPosition = position.dy - menuHeight - kMenuSpacing;

      // Ensure menu doesn't go above safe area
      if (topPosition < safeAreaTop) {
        topPosition = safeAreaTop + kSafeAreaPadding;
      }

      return topPosition -
          (context.isTablet
              ? kIconContainerHeight * 1.4
              : kIconContainerHeight);
    } else {
      // Show menu below the three-dot icon
      double topPosition = position.dy + kMenuSpacing;

      // Ensure menu doesn't go below safe area
      if (topPosition + menuHeight > screenHeight - safeAreaBottom) {
        topPosition =
            screenHeight - safeAreaBottom - menuHeight - kSafeAreaPadding;
      }

      return topPosition;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Add a transparent full-screen GestureDetector to handle taps outside
        Positioned.fill(
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTapDown: (_) => onDismiss(),
            onVerticalDragStart: (_) => onDismiss(),
            onHorizontalDragStart: (_) => onDismiss(),
            child: Container(color: Colors.transparent),
          ),
        ),
        Positioned(
          left: _calculateMenuHorizontalPosition(context),
          top: _calculateMenuPosition(context),
          child: Material(
            elevation: 8,
            color: context.colorScheme.mainNeutral,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: items.map((item) {
                return InkWell(
                  onTap: () {
                    if (item.subItems != null && item.subItems!.isNotEmpty) {
                      final box = context.findRenderObject() as RenderBox;
                      final globalPosition = box.localToGlobal(Offset.zero);
                      onSubMenuRequested(
                          context, globalPosition, item.subItems!);
                    } else {
                      item.onTap?.call();
                    }
                  },
                  child: Container(
                    width: kMenuWidth,
                    padding: EdgeInsets.only(
                      top: 12.h,
                      bottom: 12.h,
                      left: 8.w,
                      right: 14.w,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (item.isShowIconLeft)
                          SvgPicture.asset(
                            item.subItems != null && item.subItems!.isNotEmpty
                                ? Assets.icons.icArrowRight
                                : Assets.icons.icArrowUp,
                            width: context.isTablet ? 16 : 16.w,
                            height: context.isTablet ? 16 : 16.h,
                            colorFilter: ColorFilter.mode(
                              context.colorScheme.mainPrimary,
                              BlendMode.srcIn,
                            ),
                          ),
                        if (item.isShowIconLeft == false) ...[
                          context.isTablet
                              ? const SizedBox(
                                  width: 16,
                                )
                              : AppConstants.kSpacingItemW14,
                        ],
                        Expanded(
                          child: CommonText(
                            item.title,
                            style: TextStyle(
                              fontSize: context.isTablet ? 16 : 14.sp,
                              fontWeight: FontWeight.w500,
                              color: item.title == S.current.delete_note_item
                                  ? AppColors.primaryRed
                                  : context.colorScheme.mainPrimary,
                            ),
                          ),
                        ),
                        item.urlSvg?.isNotEmpty == true
                            ? SvgPicture.asset(
                                item.urlSvg!,
                                width: context.isTablet
                                    ? kIconWidth
                                    : kIconWidth.w,
                                height: context.isTablet
                                    ? kIconHeight
                                    : kIconHeight.h,
                                colorFilter:
                                    item.title == S.current.delete_note_item
                                        ? const ColorFilter.mode(
                                            AppColors.primaryRed,
                                            BlendMode.srcIn,
                                          )
                                        : ColorFilter.mode(
                                            context.colorScheme.mainPrimary,
                                            BlendMode.srcIn,
                                          ),
                              )
                            : const SizedBox.shrink(),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }
}
