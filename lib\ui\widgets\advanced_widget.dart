// ignore_for_file: constant_identifier_names

import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class AdvancedWidget extends StatefulWidget {
  final Function(String)? onSummaryStyleChanged;
  final Function(String)? onWritingStyleChanged;
  final Function(String)? onAdditionalInstructionsChanged;
  final Function(bool)? onAdvancedToggled;
  final Function(bool)? onAdvancedChanged;

  const AdvancedWidget({
    Key? key,
    this.onSummaryStyleChanged,
    this.onWritingStyleChanged,
    this.onAdditionalInstructionsChanged,
    this.onAdvancedToggled,
    this.onAdvancedChanged,
  }) : super(key: key);

  @override
  State<AdvancedWidget> createState() => _AdvancedWidgetState();
}

class _AdvancedWidgetState extends State<AdvancedWidget> {
  late bool isEnableAdvanced;
  SummaryStyle selectedSummaryStyle = SummaryStyle.Balanced;
  WritingStyle selectedWritingStyle = WritingStyle.Neutral;

  @override
  void initState() {
    isEnableAdvanced = false;
    _updateCallback();
    super.initState();
  }

  void _updateCallback() {
    if (isEnableAdvanced) {
      widget.onSummaryStyleChanged?.call(selectedSummaryStyle.name);
      widget.onWritingStyleChanged?.call(selectedWritingStyle.name);
    } else {
      widget.onSummaryStyleChanged?.call('');
      widget.onWritingStyleChanged?.call('');
    }
    widget.onAdvancedToggled?.call(isEnableAdvanced);
    widget.onAdvancedChanged?.call(isEnableAdvanced);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: CommonText(
                S.current.advanced,
                style: TextStyle(
                  color: context.colorScheme.mainGray,
                  fontWeight: FontWeight.normal,
                  fontSize: context.isTablet ? 16 : 14.sp,
                ),
              ),
            ),
            SizedBox(
              width: 44.w,
              height: 24.h,
              child: Transform.scale(
                alignment: Alignment.centerRight,
                scale: 0.8,
                child: CupertinoSwitch(
                  value: isEnableAdvanced,
                  onChanged: (value) {
                    setState(() {
                      isEnableAdvanced = value;
                    });
                    _updateCallback();
                  },
                  activeColor: context.colorScheme.mainBlue,
                  trackColor: context.colorScheme.mainGray,
                  thumbColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
        AppConstants.kSpacingItem16,
        if (isEnableAdvanced) ...[
          /// Summary Style
          CommonText(
            S.current.summary_style,
            style: TextStyle(
              color: context.colorScheme.mainGray,
              fontWeight: FontWeight.normal,
              fontSize: context.isTablet ? 16 : 14.sp,
            ),
          ),
          AppConstants.kSpacingItem8,
          Container(
            height: context.isTablet ? 48 : 48.h,
            decoration: BoxDecoration(
              color: context.colorScheme.mainNeutral,
              borderRadius: BorderRadius.circular(100.r),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton2<String>(
                value: selectedSummaryStyle.displayName,
                iconStyleData: const IconStyleData(
                  icon: SizedBox.shrink(),
                ),
                dropdownStyleData: DropdownStyleData(
                  decoration: BoxDecoration(
                    color: context.colorScheme.mainNeutral,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  padding: EdgeInsets.zero,
                  offset: Offset(0, context.isTablet ? -8 : -8.h),
                  useSafeArea: true,
                  isOverButton: false,
                ),
                customButton: Padding(
                  padding: context.isTablet
                      ? const EdgeInsets.symmetric(horizontal: 8, vertical: 8)
                      : EdgeInsets.only(
                          left: 8.w,
                          right: 12.w,
                          bottom: 8.h,
                          top: 8.h,
                        ),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        Assets.icons.icBalanced,
                        height: 32.h,
                        width: 32.h,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                      AppConstants.kSpacingItemW8,
                      Expanded(
                        child: CommonText(
                          selectedSummaryStyle.displayName,
                          style: TextStyle(
                            fontSize: context.isTablet ? 16 : 14.sp,
                            fontWeight: FontWeight.w500,
                            color: context.colorScheme.mainPrimary,
                          ),
                          maxLines: 1,
                        ),
                      ),
                      SvgPicture.asset(
                        width: 20.w,
                        height: 20.h,
                        Assets.icons.icCreateNoteDropDown,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                    ],
                  ),
                ),
                menuItemStyleData: MenuItemStyleData(
                  padding: EdgeInsets.zero,
                  height: context.isTablet ? 52 : 48.h,
                ),
                buttonStyleData: ButtonStyleData(
                  overlayColor: WidgetStateProperty.all(Colors.transparent),
                ),
                isExpanded: true,
                items: SummaryStyle.values.map((style) {
                  final isSelected = style == selectedSummaryStyle;
                  return DropdownMenuItem<String>(
                    value: style.displayName,
                    child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? context.colorScheme.mainSecondary
                              : context.colorScheme.mainNeutral,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CommonText(
                                    style.displayName,
                                    style: TextStyle(
                                      fontSize: context.isTablet ? 16 : 14.sp,
                                      fontWeight: FontWeight.w400,
                                      color: context.colorScheme.mainPrimary,
                                    ),
                                    maxLines: 1,
                                  ),
                                  CommonText(
                                    style.description,
                                    style: TextStyle(
                                      fontSize: context.isTablet ? 12 : 10.sp,
                                      fontWeight: FontWeight.w400,
                                      color: context.colorScheme.mainPrimary
                                          .withOpacity(0.38),
                                    ),
                                    maxLines: 1,
                                  ),
                                ],
                              ),
                            ),
                            if (isSelected)
                              Icon(
                                Icons.check,
                                size: 24,
                                color: context.colorScheme.mainBlue,
                              ),
                          ],
                        )),
                  );
                }).toList(),
                onChanged: (newValue) {
                  setState(() {
                    selectedSummaryStyle = SummaryStyle.values
                        .firstWhere((e) => e.displayName == newValue!);
                  });
                  widget.onSummaryStyleChanged?.call(selectedSummaryStyle.name);
                },
              ),
            ),
          ),

          /// End Summary Style

          AppConstants.kSpacingItem16,

          /// Writing Style
          CommonText(
            S.current.writing_style,
            style: TextStyle(
              color: context.colorScheme.mainGray,
              fontWeight: FontWeight.normal,
              fontSize: context.isTablet ? 16 : 14.sp,
            ),
          ),
          AppConstants.kSpacingItem8,
          Container(
            height: context.isTablet ? 48 : 48.h,
            decoration: BoxDecoration(
              color: context.colorScheme.mainNeutral,
              borderRadius: BorderRadius.circular(100.r),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton2<String>(
                value: selectedWritingStyle.displayName,
                iconStyleData: const IconStyleData(
                  icon: SizedBox.shrink(),
                ),
                dropdownStyleData: DropdownStyleData(
                  decoration: BoxDecoration(
                    color: context.colorScheme.mainNeutral,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  padding: EdgeInsets.zero,
                  offset: Offset(0, context.isTablet ? -8 : -8.h),
                  useSafeArea: true,
                  isOverButton: false,
                ),
                customButton: Padding(
                  padding: context.isTablet
                      ? const EdgeInsets.symmetric(horizontal: 8, vertical: 8)
                      : EdgeInsets.only(
                          left: 8.w,
                          right: 12.w,
                          bottom: 8.h,
                          top: 8.h,
                        ),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        Assets.icons.icNeutral,
                        height: 32.h,
                        width: 32.h,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                      AppConstants.kSpacingItemW8,
                      Expanded(
                        child: CommonText(
                          selectedWritingStyle.displayName,
                          style: TextStyle(
                            fontSize: context.isTablet ? 16 : 14.sp,
                            fontWeight: FontWeight.w500,
                            color: context.colorScheme.mainPrimary,
                          ),
                          maxLines: 1,
                        ),
                      ),
                      SvgPicture.asset(
                        width: 20.w,
                        height: 20.h,
                        Assets.icons.icCreateNoteDropDown,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                    ],
                  ),
                ),
                menuItemStyleData: MenuItemStyleData(
                  padding: EdgeInsets.zero,
                  height: context.isTablet ? 52 : 48.h,
                ),
                buttonStyleData: ButtonStyleData(
                  overlayColor: WidgetStateProperty.all(Colors.transparent),
                ),
                isExpanded: true,
                items: WritingStyle.values.map((style) {
                  final isSelected = style == selectedWritingStyle;
                  return DropdownMenuItem<String>(
                    value: style.displayName,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? context.colorScheme.mainSecondary
                            : context.colorScheme.mainNeutral,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CommonText(
                                  style.displayName,
                                  style: TextStyle(
                                    fontSize: context.isTablet ? 16 : 14.sp,
                                    fontWeight: FontWeight.w400,
                                    color: context.colorScheme.mainPrimary,
                                  ),
                                  maxLines: 1,
                                ),
                                CommonText(
                                  style.description,
                                  style: TextStyle(
                                    fontSize: context.isTablet ? 12 : 10.sp,
                                    fontWeight: FontWeight.w400,
                                    color: context.colorScheme.mainPrimary
                                        .withOpacity(0.38),
                                  ),
                                  maxLines: 1,
                                ),
                              ],
                            ),
                          ),
                          if (isSelected) // Assuming isSelected is available in your context
                            Icon(
                              Icons.check,
                              size: 24,
                              color: context.colorScheme.mainBlue,
                            ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (newValue) {
                  setState(() {
                    selectedWritingStyle = WritingStyle.values
                        .firstWhere((e) => e.displayName == newValue!);
                  });
                  widget.onWritingStyleChanged?.call(selectedWritingStyle.name);
                },
              ),
            ),
          ),

          /// End Writing Style
          AppConstants.kSpacingItem16,

          /// Additional Instructions (opt.)
          CommonText(
            S.current.additional_ins,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: context.isTablet ? 16 : 14.sp,
              color: context.colorScheme.mainGray,
            ),
          ),
          AppConstants.kSpacingItem8,
          RoundedTextFieldWithIcon(
            svgIconPath: Assets.icons.icAdditional,
            hintText: S.current.add_focus,
            onAdditionalInstructionsChanged:
                widget.onAdditionalInstructionsChanged,
          ),
        ]
      ],
    );
  }
}

class RoundedTextFieldWithIcon extends StatefulWidget {
  final String svgIconPath;
  final String hintText;
  final Function(String)? onAdditionalInstructionsChanged;

  const RoundedTextFieldWithIcon({
    Key? key,
    required this.svgIconPath,
    required this.hintText,
    this.onAdditionalInstructionsChanged,
  }) : super(key: key);

  @override
  RoundedTextFieldWithIconState createState() =>
      RoundedTextFieldWithIconState();
}

class RoundedTextFieldWithIconState extends State<RoundedTextFieldWithIcon> {
  final TextEditingController _controller = TextEditingController();
  int _charCount = 0;
  final int _maxLength = 50;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          padding: EdgeInsets.only(right: 8.w),
          height: context.isTablet ? 48 : 48.h,
          decoration: BoxDecoration(
            color: context.colorScheme.mainNeutral,
            borderRadius: BorderRadius.circular(100.r),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                child: SvgPicture.asset(
                  widget.svgIconPath,
                  width: context.isTablet ? 32 : 32.w,
                  height: context.isTablet ? 32 : 32.h,
                  colorFilter: ColorFilter.mode(
                    context.colorScheme.mainPrimary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _controller,
                  maxLength: _maxLength,
                  style: TextStyle(
                    fontSize: context.isTablet ? 16 : 14.sp,
                    fontWeight: FontWeight.w400,
                    color: context.colorScheme.mainPrimary,
                  ),
                  decoration: InputDecoration(
                    hintText: widget.hintText,
                    hintStyle: TextStyle(
                      fontSize: context.isTablet ? 14 : 12.sp,
                      fontWeight: FontWeight.w400,
                      color: context.colorScheme.mainPrimary.withOpacity(0.38),
                    ),
                    border: InputBorder.none,
                    counterText: "",
                  ),
                  onChanged: (value) {
                    setState(() {
                      _charCount = value.length;
                    });
                    widget.onAdditionalInstructionsChanged?.call(value);
                  },
                ),
              ),
              SizedBox(width: 16.w),
            ],
          ),
        ),
        AppConstants.kSpacingItem4,
        CommonText(
          '$_charCount/$_maxLength',
          style: TextStyle(
            fontSize: context.isTablet ? 14 : 12.sp,
            fontWeight: FontWeight.w400,
            color: context.colorScheme.mainGray.withOpacity(0.38),
          ),
        ),
      ],
    );
  }
}

enum SummaryStyle {
  Short,
  Balanced,
  Comprehensive;

  String get displayName {
    switch (this) {
      case SummaryStyle.Short:
        return S.current.short;
      case SummaryStyle.Balanced:
        return S.current.balanced;
      case SummaryStyle.Comprehensive:
        return S.current.comprehensive;
    }
  }

  String get description {
    switch (this) {
      case SummaryStyle.Short:
        return S.current.short_description;
      case SummaryStyle.Balanced:
        return S.current.balanced_description;
      case SummaryStyle.Comprehensive:
        return S.current.comprehensive_description;
    }
  }
}

enum WritingStyle {
  Friendly,
  Neutral,
  Professional;

  String get displayName {
    switch (this) {
      case WritingStyle.Friendly:
        return S.current.friendly;
      case WritingStyle.Neutral:
        return S.current.neutral;
      case WritingStyle.Professional:
        return S.current.professional_style;
    }
  }

  String get description {
    switch (this) {
      case WritingStyle.Friendly:
        return S.current.friendly_description;
      case WritingStyle.Neutral:
        return S.current.neutral_description;
      case WritingStyle.Professional:
        return S.current.professional_description;
    }
  }
}
