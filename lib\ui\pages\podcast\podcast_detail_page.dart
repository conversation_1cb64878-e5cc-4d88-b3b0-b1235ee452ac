import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/shorts_detail/short_details_state.dart';

class PodcastDetailPage extends StatefulWidget {
  static const routeName = 'PodcastDetailPage';

  final int durationOption;
  final String voiceId;
  final String noteId;
  final String title;
  final String videoId;

  const PodcastDetailPage({
    super.key,
    this.durationOption = 60,
    this.voiceId = '',
    required this.title,
    this.noteId = '',
    this.videoId = '',
  });

  @override
  State<PodcastDetailPage> createState() => _PodcastDetailPageState();
}

class _PodcastDetailPageState
    extends BasePageStateDelegate<PodcastDetailPage, ShortsDetailCubit> {
  // UI element keys for sharing and downloading
  final GlobalKey _shareButtonKey = GlobalKey();
  final GlobalKey _downloadButtonKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // Initialize cubit with widget configuration
    cubit.initData(
      false, // isCaptionEnabled is not relevant for podcast
      true,
      false, // isCreateAudioShorts = true for podcast
    );

    cubit.generateAudio(
      widget.noteId,
      widget.durationOption,
      widget.voiceId,
    );
  }

  @override
  void dispose() {
    // Clean up resources
    cubit.videoController?.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colorScheme.mainBackground,
      body: SafeArea(
        child: Column(
          children: [
            ShortsDetailCommonWidgets.buildTopBar(
              context: context,
              titleNotifier: cubit.titleNotifier,
              onCancelTap: _handleOnCancelRecord,
              overrideTitle: S.current.podcast_name,
            ),
            Expanded(
              child: _buildPodcastPlayer(),
            ),
            AppConstants.kSpacingItem16,
            _buildBottomSection(),
          ],
        ),
      ),
    );
  }

  /// Builds the bottom section with controls and temporary content message
  Widget _buildBottomSection() {
    return BlocBuilder<ShortsDetailCubit, ShortDetailsState>(
      buildWhen: (previous, current) =>
          previous.processingStatus != current.processingStatus,
      builder: (context, state) {
        if (state.processingStatus != ProcessingStatus.complete) {
          return const SizedBox.shrink();
        }

        return Column(
          children: [
            ShortsDetailCommonWidgets.buildBottomControls(
              context: context,
              shareButtonKey: _shareButtonKey,
              downloadButtonKey: _downloadButtonKey,
              isAudio: true,
              isTablet: cubit.appCubit.isTablet,
              onDownloadOrShare: (
                context, {
                required buttonKey,
                required isDownload,
              }) {
                cubit.downloadVideo(
                  context,
                  buttonKey: buttonKey,
                  isDownload: isDownload,
                  isShort: false,
                );
              },
            ),
            ShortsDetailCommonWidgets.buildTemporaryContentMessage(
              context: context,
              isAudio: true,
              isTablet: cubit.appCubit.isTablet,
            ),
          ],
        );
      },
    );
  }

  /// Builds the podcast player UI
  Widget _buildPodcastPlayer() {
    return BlocBuilder<ShortsDetailCubit, ShortDetailsState>(
      buildWhen: (previous, current) =>
          previous.processingStatus != current.processingStatus ||
          previous.audioFilePath != current.audioFilePath,
      builder: (context, state) {
        // Handle error state
        if (state.processingStatus == ProcessingStatus.error) {
          return const Center(child: Text('Failed to generate AI audio'));
        }

        // Determine if we need to show a processing overlay
        Widget? overlay;
        if (state.processingStatus != ProcessingStatus.complete) {
          overlay = ShortsDetailCommonWidgets.buildProcessingOverlay(
            context: context,
            status: state.processingStatus,
          );
        }

        // Show either the audio player or loading animation
        return state.audioFilePath.isNotEmpty
            ? _buildPodcastPlayerContent(state.audioFilePath)
            : _buildPodcastLoadingContent(overlay);
      },
    );
  }

  /// Builds the podcast player content when audio is ready
  Widget _buildPodcastPlayerContent(String audioFilePath) {
    return Column(
      children: [
        AppConstants.kSpacingItem120,
        // Podcast image
        SvgPicture.asset(
          Assets.images.imgPodcast,
          width: 240,
          height: 240,
        ),
        AppConstants.kSpacingItem130,
        // Audio player widget
        CustomAudioPlayerWidget(
          audioFilePath: audioFilePath,
          isCommunityNote: false,
          fullScreen: true,
          isShowMore: false,
          backgroundColor: context.colorScheme.mainSecondary,
        ),
      ],
    );
  }

  /// Builds the loading content while podcast is being generated
  Widget _buildPodcastLoadingContent(Widget? overlay) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Loading animation
        Center(
          child: Lottie.asset(Assets.videos.podcastAudioGenerating),
        ),
        // Processing overlay if needed
        if (overlay != null)
          Positioned(
            left: 0,
            right: 0,
            bottom: 50,
            child: overlay,
          ),
      ],
    );
  }

  /// Shows a confirmation dialog when user attempts to cancel/close
  void _handleOnCancelRecord() {
    ShortsDetailCommonWidgets.showCancelConfirmationDialog(context);
  }
}
