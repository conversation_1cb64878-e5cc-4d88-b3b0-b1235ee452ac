import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/ui/pages/purchase/cubit/purchase_state.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import '../../../../base/app/app_state.dart';
import '../../../../lib.dart';

class PurchaseCubit extends BaseCubit<PurchaseState> {
  PurchaseCubit(super.initialState);

  final _localService = GetIt.instance.get<LocalService>();
  final _creditApiServiceImpl = GetIt.instance.get<CreditApiServiceImpl>();

  late List<PackageProductDto> _packages;
  late List<PackageProductDto> _trialPackages;
  late List<Package> _rcPackages;
  late List<Package> _rcTrialPackages;

  int _selectedIndex = -1;
  PurchasePageFrom _from = PurchasePageFrom.iapSplash;
  Package? get getRcYearTrialPackage {
    if (_rcTrialPackages.length > 2) {
      return _rcTrialPackages[2];
    }
    return null;
  }

  void selectPackage(int index) {
    _selectedIndex = index;
    _packages = _packages
        .mapIndexed((i, element) => i == index
            ? element.copyWith(isSelected: true)
            : element.copyWith(isSelected: false))
        .toList();
    emit(state.copyWith(listPackage: _packages, selectedIndex: _selectedIndex));

    switch (index) {
      case 0:
        AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.iap_week_click);
      case 1:
        AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.iap_month_click);
      case 2:
        AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.iap_year_click);
    }
  }

  void emitEnableSwitch(bool isToggle) {
    emit(state.copyWith(isEnableSwitch: isToggle));
  }

  int getSelectedIndex() => _selectedIndex;

  void initOfferings() async {
    // Try to initialize with existing packages first
    if (_initializeWithExistingPackages()) {
      return; // Successfully initialized with existing packages
    }

    // Retry fetching packages if needed
    await appCubit.retryFetchDefaultPackageMainIAP();
    final isRetryFail = !_initializeWithExistingPackages();
    if (isRetryFail) {
      AnalyticsService.logAnalyticsEventNoParam(
          eventName: 'rc_get_default_trial_fail_cubit');
    }
  }

  bool _initializeWithExistingPackages() {
    _rcPackages = appCubit.getRCPackages();
    _rcTrialPackages = appCubit.getRCTrialPackages();
    _packages = appCubit.getViewPackages();
    _trialPackages = appCubit.getViewTrialPackages();
    if (_rcPackages.isEmpty ||
        _packages.isEmpty ||
        _rcTrialPackages.isEmpty ||
        _trialPackages.isEmpty) {
      return false; // Need to retry package fetch
    }

    _selectedIndex = _getSelectedPackageIndex();

    emit(state.copyWith(
      listPackage: _packages,
      selectedIndex: _selectedIndex,
    ));

    return true;
  }

  int _getSelectedPackageIndex() {
    final selectedIndex = _packages.indexWhere((e) => e.isSelected);
    return selectedIndex == -1 ? 0 : selectedIndex;
  }

  Future<void> onRestorePurchase() async {
    runCubitCatching(action: () async {
      try {
        await setAttributes();
        CustomerInfo customerInfo = await Purchases.restorePurchases();
        if (customerInfo.entitlements
                    .all[AppConstants.revenueCatProEntitlementId]?.isActive ==
                true ||
            customerInfo
                    .entitlements
                    .all[AppConstants.revenueCatEssentialLifetimeEntitlementId]
                    ?.isActive ==
                true) {
          final creditInfo = await _creditApiServiceImpl.fetchDataCredit();
          final userType = mapUserTypeStringToEnum(creditInfo.userType);
          if (userType == UserType.pro || userType == UserType.proLite) {
            final user = appCubit.getAppUser().copyWith(userType: userType);
            appCubit.updateAppUser(user);
            _localService.saveAppUser(user);
            emit(
              state.copyWith(oneShotEvent: PurchaseOneShotEvent.restoreSuccess),
            );
          } else {
            emit(
              state.copyWith(
                  oneShotEvent: PurchaseOneShotEvent.onNothingToRestore),
            );
          }
        } else {
          emit(
            state.copyWith(
                oneShotEvent: PurchaseOneShotEvent.onNothingToRestore),
          );
        }
      } on PlatformException {
        emit(
          state.copyWith(oneShotEvent: PurchaseOneShotEvent.onNothingToRestore),
        );
      }
    });
  }

  Future<void> setAttributes() async {
    Map<String, String> attributes = {
      "userID": GetIt.instance.get<LocalService>().getAppUser().id,
      "deviceToken": GetIt.instance.get<LocalService>().getFcmToken() ?? "null",
    };
    // Set attributes for webhook
    await Purchases.setAttributes(attributes);
  }

  Future<void> purchaseProduct({bool isTrial = false}) async {
    return runCubitCatching(
        handleLoading: false,
        action: () async {
          try {
            if ((_selectedIndex == -1 && !isTrial) ||
                (getRcYearTrialPackage == null && isTrial)) {
            } else {
              CommonDialogs.showLoadingDialog();
              await setAttributes();
              final purchaserInfo = await Purchases.purchasePackage(isTrial
                  ? getRcYearTrialPackage!
                  : _rcPackages[_selectedIndex]);
              String entitlementID = AppConstants.revenueCatProEntitlementId;
              if (purchaserInfo.entitlements.all[entitlementID]?.isActive ==
                  true) {
                AnalyticsService.logAnalyticsEventNoParam(
                    eventName: EventName.iap_purchase_success);
                _logEventAFRevenue(isTrial
                    ? getRcYearTrialPackage!
                    : _rcPackages[_selectedIndex]);
                CommonDialogs.closeLoading();
                CommonDialogs.showLoadingDialog(
                    dialogText: ValueNotifier(S.current.loading_content));
                // Xử lý khi mua thành công
                Purchases.syncPurchases();
                Future.delayed(const Duration(seconds: 3), () {
                  final user =
                      appCubit.getAppUser().copyWith(userType: UserType.pro);
                  appCubit.updateAppUser(user);
                  _localService.saveAppUser(user);
                  CommonDialogs.closeLoading();
                  emit(
                    state.copyWith(
                        oneShotEvent: PurchaseOneShotEvent.purchaseSuccess),
                  );
                });
              } else {
                CommonDialogs.closeLoading();
                emit(
                  state.copyWith(
                      oneShotEvent: PurchaseOneShotEvent.purchaseFail),
                );
              }
            }
          } on PlatformException catch (e) {
            CommonDialogs.closeLoading();
            bool isUserCancel = e.code == "1";
            if (!isUserCancel) {
              final platform = Platform.isAndroid ? 'and' : 'ios';
              AnalyticsService.logAnalyticsEventNoParam(
                  eventName: 'payF_${platform}_${e.code}');
              AnalyticsService.logAnalyticsEventNoParam(
                  eventName: 'payF_${platform}_${e.message}');
              emit(
                state.copyWith(oneShotEvent: PurchaseOneShotEvent.purchaseFail),
              );
            }
          }
        });
  }

  void popOnPurchaseSuccess(BuildContext context) {
    switch (_from) {
      case PurchasePageFrom.iapSplash:
      case PurchasePageFrom.iapOnboarding:
        appCubit.setCurrentScreen(AppScreen.home);
        break;
      case PurchasePageFrom.iapDialogSaleOff:
        showNotiOrRate(context);
        break;
      default:
        Navigator.pop(context, true);
        break;
    }
  }

  void resetEnumState() {
    if (!isClosed) {
      emit(state.copyWith(oneShotEvent: PurchaseOneShotEvent.none));
    }
  }

  void setVisibilityCloseButton() {
    Future.delayed(const Duration(seconds: 1), () {
      emit(state.copyWith(isVisibleClose: true));
    });
  }

  void showButtonClose() {
    emit(state.copyWith(isVisibleClose: true));
  }

  void onCloseButtonPressed(BuildContext context) {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.iap_close,
    );

    switch (_from) {
      case PurchasePageFrom.iapSplash:
        appCubit.setCurrentScreen(AppScreen.home);
        break;
      case PurchasePageFrom.iapOnboarding:
        appCubit.setCurrentScreen(AppScreen.home);
        break;
      case PurchasePageFrom.iapCreateNote || PurchasePageFrom.iapRecordScreen:
        showReffer(context);
        break;
      case PurchasePageFrom.iapHome:
        showSaleOffPage(context);
        break;
      case PurchasePageFrom.iapDialogSaleOff:
        showNotiOrRate(context);
        break;
      default:
        Navigator.pop(context);
        break;
    }
  }

  void showNotiOrRate(BuildContext context) async {
    Navigator.pop(context);
    if (await MyUtils.isNotificationPermanentlyDeniedOrAccepted()) {
      MyUtils.requestReview();
    } else {
      g.get<FirebaseNotificationService>().requestPermission();
    }
  }

  void showSaleOffPage(BuildContext context) {
    Navigator.pushReplacement(
      context,
      CupertinoPageRoute(
        builder: (context) => const PurchasePage(
          from: PurchasePageFrom.iapPayMain,
          isUiSpecial: true,
        ),
        fullscreenDialog: true,
      ),
    );
  }

  void showReffer(BuildContext context) {
    Navigator.pop(context, false);
    try {
      if (_localService.getNumberOfReferralDialogShown() < 3) {
        showReferralDialog(context);
        _localService.setNumberOfReferralDialogShown(
            _localService.getNumberOfReferralDialogShown() + 1);
      }
    } catch (e) {
      debugPrint('Error: $e');
    }
  }

  void logEventShowPackagesButtonClicked() {}

  void logEventOnConfirmTrialClick() {}

  void trackingShowPurchase(PurchasePageFrom from, bool isUiSpecial) {
    _from = from;
    if (isUiSpecial) {
      AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.iap_sale_off);
    } else {
      AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.iap_show);
    }
  }

  void _logEventAFRevenue(Package rcPackage) {
    final storeProduct = rcPackage.storeProduct;
    AppsFlyerService.logEventPurchase(
        currency: storeProduct.currencyCode,
        amount: storeProduct.price,
        contentId: storeProduct.identifier,
        contentType: 'subscription');
  }
}
