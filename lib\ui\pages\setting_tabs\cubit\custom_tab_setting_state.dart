import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:note_x/base/base_state.dart';

part 'custom_tab_setting_state.freezed.dart';

@freezed
class CustomTabSettingState extends BaseState with _$CustomTabSettingState {
  const factory CustomTabSettingState({
    @Default([]) List<String> selectedTabs,
    @Default([]) List<String> allTabs,
    @Default([]) List<String> alwaysSelected,
    @Default(false) bool isShowUpdateDialog,
    @Default(false) bool isSaveButtonEnabled,
  }) = _CustomTabSettingState;
}
