import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

void showModalBottomSheetExport(
  BuildContext context,
  ExportType exportType,
  Func1<ExportNoteFormat, void> onPressed,
  ValueNotifier<Map<ExportNoteFormat, bool>> loadingNoteStates,
) {
  void checkAndCloseBottomSheet() {
    if (!loadingNoteStates.value.values.contains(true)) {
      Navigator.of(context).pop();
      loadingNoteStates.removeListener(checkAndCloseBottomSheet);
    }
  }

  loadingNoteStates.addListener(checkAndCloseBottomSheet);

  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (BuildContext context) {
      return Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          decoration: BoxDecoration(
            color: context.colorScheme.mainNeutral,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(20.r),
            ),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 24.h,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    exportType.displayTitle,
                    style: TextStyle(
                      fontSize: context.isTablet ? 22 : 20.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      // Remove the listener when manually closing
                      loadingNoteStates
                          .removeListener(checkAndCloseBottomSheet);
                      Navigator.of(context).pop();
                    },
                    child: SvgPicture.asset(
                      Assets.icons.icCloseWhite,
                      width: context.isTablet ? 24 : 24.w,
                      height: context.isTablet ? 24 : 24.h,
                      colorFilter: ColorFilter.mode(
                        context.colorScheme.mainPrimary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ],
              ),
              AppConstants.kSpacingItem12,
              ValueListenableBuilder<Map<ExportNoteFormat, bool>>(
                valueListenable: loadingNoteStates,
                builder: (context, loadingNoteStates, child) {
                  return Column(
                    children: exportType.exportFormatList.where((format) {
                      if (format == ExportNoteFormat.srt) {
                        return true;
                      }
                      return true;
                    }).map((format) {
                      return Column(
                        children: [
                          ExportOptionItem(
                            text: format.displayName,
                            isLoading: loadingNoteStates[format] ?? false,
                            onTap: () {
                              onPressed(format);
                            },
                          ),
                          if (format != exportType.exportFormatList.last)
                            AppConstants.kSpacingItem12,
                        ],
                      );
                    }).toList(),
                  );
                },
              ),
            ],
          ),
        ),
      );
    },
  ).then((_) {
    loadingNoteStates.removeListener(checkAndCloseBottomSheet);
  });
}
