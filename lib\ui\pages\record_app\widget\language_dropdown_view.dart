import 'package:el_tooltip/el_tooltip.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'dart:ui';
import 'package:note_x/base/base.dart';
import 'package:note_x/generated/l10n.dart';
import '../../../widgets/common/common_text.dart';

class LanguageDropdownView extends StatefulWidget {
  final Function(Language?) onChanged;
  final Function(bool)? onMenuStateChanged;
  final String? initialLanguageCode;
  final bool isDisabled;
  final LanguageDropdownUseCase useCase;
  final LanguageDropdownFrom from;
  final ElTooltipController? tooltipController;
  final Widget? leadingIcon;
  final bool isCreateNoteLanguage;

  const LanguageDropdownView({
    Key? key,
    required this.onChanged,
    this.onMenuStateChanged,
    this.initialLanguageCode,
    this.isDisabled = false,
    required this.useCase,
    required this.from,
    this.tooltipController,
    this.leadingIcon,
    this.isCreateNoteLanguage = true,
  }) : super(key: key);

  @override
  LanguageDropdownViewState createState() => LanguageDropdownViewState();
}

class LanguageDropdownViewState extends State<LanguageDropdownView> {
  Language? _selectedLanguage;
  Language? _deviceLanguage;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isShowingMenu = false;
  List<Language> _supportedLanguages = supportedLanguages;

  final double _menuMaxHeight = 200.0;
  final ScrollController _scrollController = ScrollController();
  final _localService = GetIt.instance.get<LocalService>();

  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<Language> _filteredLanguages = [];

  @override
  void initState() {
    super.initState();
    _filteredLanguages = _supportedLanguages;
    _detectDeviceLanguage();
    _initializeLanguage();
  }

  // Add this method to the _LanguageDropdownViewState class to handle widget updates
  @override
  void didUpdateWidget(LanguageDropdownView oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the useCase has changed
    if (widget.useCase != oldWidget.useCase) {
      // Specifically handle transition from translateLanguageWithAuto to defaultTranslateLanguage
      if (oldWidget.useCase ==
              LanguageDropdownUseCase.translateLanguageWithAuto &&
          widget.useCase == LanguageDropdownUseCase.defaultTranslateLanguage) {
        // Update supported languages for defaultTranslateLanguage (without 'Auto')
        _supportedLanguages = translateLanguages;

        // Reset filtered languages
        _filteredLanguages = _supportedLanguages;

        // If 'Auto' was selected, reset to saved language or default
        if (_selectedLanguage != null && _selectedLanguage!.code == 'auto') {
          // Get saved language or use first available
          final savedLanguageCode =
              _localService.getSavedLanguageCode(widget.from.toString());

          if (savedLanguageCode != null) {
            _selectedLanguage = _supportedLanguages.firstWhere(
              (lang) => lang.code == savedLanguageCode,
              orElse: () => _supportedLanguages.first,
            );
          } else {
            _selectedLanguage = _supportedLanguages.first;
          }

          // Notify parent of language change
          setState(() {}); // Make sure UI updates with the new selection
          widget.onChanged(_selectedLanguage);
        }
      } else {
        // Handle other useCase changes
        if (widget.useCase ==
                LanguageDropdownUseCase.defaultTranslateLanguage ||
            widget.useCase ==
                LanguageDropdownUseCase.translateLanguageWithAuto) {
          _supportedLanguages = translateLanguages;
        } else {
          _supportedLanguages = supportedLanguages;
        }

        // Add 'Auto' for translateLanguageWithAuto
        if (widget.useCase ==
            LanguageDropdownUseCase.translateLanguageWithAuto) {
          _supportedLanguages = [
            Language('auto', S.current.auto),
            ..._supportedLanguages.where((lang) => lang.code != 'auto'),
          ];

          // Set 'Auto' as default if not already set
          if (_selectedLanguage == null) {
            _selectedLanguage = _supportedLanguages.first;
            widget.onChanged(_selectedLanguage);
          }
        }

        _filteredLanguages = _supportedLanguages;
      }

      // Rebuild the overlay if it's currently showing
      if (_isShowingMenu) {
        _hideLanguageMenu();
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && _isShowingMenu) {
            _showLanguageMenu();
          }
        });
      }
    }

    // Handle initialLanguageCode changes
    if (widget.initialLanguageCode != oldWidget.initialLanguageCode &&
        widget.initialLanguageCode != null) {
      _selectedLanguage = _supportedLanguages.firstWhere(
        (lang) => lang.code == widget.initialLanguageCode,
        orElse: () => _supportedLanguages
            .first, // Use first language instead of 'Not found!'
      );
      // Just notify parent about the selected language without logging an event
      // This is a programmatic update, not a user selection
      widget.onChanged(_selectedLanguage);
    }
  }

  void _detectDeviceLanguage() {
    final Locale deviceLocale = PlatformDispatcher.instance.locale;
    final String languageCode = deviceLocale.languageCode;

    // Try to find exact match first
    _deviceLanguage = _supportedLanguages.firstWhere(
      (lang) => lang.code == languageCode,
      orElse: () => _supportedLanguages.firstWhere(
        // If no exact match, try to find a language with matching base code
        (lang) => lang.code.startsWith(languageCode),
        orElse: () => const Language('err', 'Not found!'),
      ),
    );
  }

  void _toggleLanguageMenu() {
    if (widget.isDisabled) return;

    setState(() {
      _isShowingMenu = !_isShowingMenu;
    });

    // Notify parent widget about the menu state change if callback provided
    if (widget.onMenuStateChanged != null) {
      widget.onMenuStateChanged!(_isShowingMenu);
    }

    // Use postFrameCallback to schedule overlay operations AFTER the build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isShowingMenu) {
        _showLanguageMenu();
      } else {
        _hideLanguageMenu();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: widget.isDisabled ? null : _toggleLanguageMenu,
        child: Opacity(
          opacity: widget.isDisabled ? 0.5 : 1.0,
          child: Container(
            height: context.isTablet ? 48 : 48.h,
            padding: context.isTablet
                ? const EdgeInsets.symmetric(horizontal: 12, vertical: 8)
                : EdgeInsets.only(
                    left: 8.w,
                    right: 12.w,
                    top: 8.h,
                    bottom: 8.h,
                  ),
            decoration: BoxDecoration(
              color: widget.isCreateNoteLanguage
                  ? context.colorScheme.mainNeutral
                  : context.colorScheme.mainSecondary,
              borderRadius: BorderRadius.circular(24.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  Assets.icons.icLanguage,
                  height: context.isTablet ? 32 : 32.w,
                  width: context.isTablet ? 32 : 32.w,
                  colorFilter: ColorFilter.mode(
                    context.colorScheme.mainPrimary,
                    BlendMode.srcIn,
                  ),
                ),
                AppConstants.kSpacingItemW8,
                Expanded(
                  child: CommonText(
                    // Improved text display logic with safeguards
                    widget.useCase ==
                            LanguageDropdownUseCase.translateLanguageWithAuto
                        ? (_selectedLanguage?.name ?? S.current.auto)
                        : (_selectedLanguage?.name ??
                            S.current.select_language),
                    style: TextStyle(
                      height: 1,
                      fontSize: context.isTablet ? 16 : 14.sp,
                      fontWeight: FontWeight.w500,
                      color: context.colorScheme.mainPrimary,
                    ),
                  ),
                ),
                AppConstants.kSpacingItemW4,
                AbsorbPointer(
                  child: ElTooltip(
                    content: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Transform.scale(
                          scale: 1.5,
                          child: Text('•',
                              style: TextStyle(
                                fontSize: context.isTablet ? 14 : 12.sp,
                                fontWeight: FontWeight.w400,
                                color: AppColors.white,
                              )),
                        ),
                        AppConstants.kSpacingItemW8,
                        Expanded(
                          child: CommonText(
                            S.current.tool_tip_language,
                            style: TextStyle(
                              fontSize: context.isTablet ? 14 : 12.sp,
                              fontWeight: FontWeight.w400,
                              color: context.colorScheme.themeWhite,
                            ),
                            maxLines: 3,
                            softWrap: true,
                          ),
                        ),
                      ],
                    ),
                    radius: Radius.circular(16.r),
                    position: ElTooltipPosition.bottomEnd,
                    color: context.colorScheme.mainBlue,
                    controller: widget.tooltipController,
                    timeout: const Duration(seconds: 3),
                    child: SvgPicture.asset(
                      width: 20.w,
                      height: 20.h,
                      Assets.icons.icCreateNoteDropDown,
                      colorFilter: ColorFilter.mode(
                        context.colorScheme.mainPrimary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _initializeLanguage() {
    if (widget.initialLanguageCode != null) {
      _selectedLanguage = _supportedLanguages.firstWhere(
        (lang) => lang.code == widget.initialLanguageCode,
        orElse: () => const Language('err', 'Not found!'),
      );
    }

    // Modify the supported languages list based on useCase
    if (widget.useCase == LanguageDropdownUseCase.defaultTranslateLanguage ||
        widget.useCase == LanguageDropdownUseCase.translateLanguageWithAuto) {
      _supportedLanguages = translateLanguages;
    }

    if (widget.useCase == LanguageDropdownUseCase.translateLanguageWithAuto) {
      // Create a new list with 'Auto' instead of 'Multi-language'
      _supportedLanguages = [
        Language('auto', S.current.auto),
        ..._supportedLanguages,
      ];

      // Set 'Auto' as default for webLink
      _selectedLanguage ??= _supportedLanguages.firstWhere(
        (lang) => lang.code == 'auto',
        orElse: () => const Language('err', 'Not found!'),
      );
    }

    final savedLanguageCode =
        _localService.getSavedLanguageCode(widget.from.toString());
    if (savedLanguageCode != null) {
      _selectedLanguage = _supportedLanguages.firstWhere(
        (lang) => lang.code == savedLanguageCode,
        orElse: () => const Language('err', 'Not found!'),
      );
    }

    // Silently update the parent with the selected language
    // We use a direct call to avoid triggering event tracking during initialization
    if (_selectedLanguage != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.onChanged(_selectedLanguage);
        }
      });
    }
  }

  void _showLanguageMenu() {
    if (!mounted) return;
    _overlayEntry?.remove();
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideLanguageMenu() {
    _focusNode.unfocus(); // Unfocus search bar
    if (!mounted) return;

    _overlayEntry?.remove();
    _overlayEntry = null;

    // Update state if menu is open
    if (_isShowingMenu) {
      setState(() {
        _isShowingMenu = false; // Set state is hidden
      });

      // Notify parent widget if callback provided
      if (widget.onMenuStateChanged != null) {
        widget.onMenuStateChanged!(false);
      }
    }
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var size = renderBox.size;

    return OverlayEntry(
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Stack(
          children: [
            Positioned.fill(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  _focusNode.unfocus();
                  _hideLanguageMenu();
                },
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),
            Positioned(
              width: size.width,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: Offset(0.0, size.height + 8),
                child: Material(
                  elevation: 8,
                  color: context.colorScheme.mainNeutral,
                  borderRadius: BorderRadius.circular(16.r),
                  child: SizedBox(
                    height: _menuMaxHeight,
                    child: Column(
                      children: [
                        // Search bar
                        Container(
                          margin: EdgeInsets.all(8.w),
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                          ),
                          decoration: BoxDecoration(
                            color: context.colorScheme.mainSecondary,
                            borderRadius: BorderRadius.circular(24.r),
                          ),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                isReverseView()
                                    ? Assets.icons.icFlipSearchEnable
                                    : Assets.icons.icSearchEnable,
                                width: context.isTablet ? 16 : 16.w,
                                height: context.isTablet ? 16 : 16.h,
                                colorFilter: ColorFilter.mode(
                                  context.colorScheme.mainGray,
                                  BlendMode.srcIn,
                                ),
                              ),
                              Expanded(
                                child: SizedBox(
                                  height: context.isTablet ? 40 : 40.h,
                                  child: CupertinoTextField(
                                    controller: _searchController,
                                    focusNode: _focusNode,
                                    autofocus: true,
                                    cursorColor: context.colorScheme.mainBlue,
                                    onChanged: (value) {
                                      if (value.endsWith('📌')) {
                                        _searchController.clear();
                                      }
                                      final trimmedValue = value.trim();
                                      setState(
                                          () => _filterLanguages(trimmedValue));
                                    },
                                    placeholder: S.current.search,
                                    placeholderStyle: TextStyle(
                                      color: context.colorScheme.mainGray,
                                      fontSize: context.isTablet ? 16 : 14.sp,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    style: TextStyle(
                                      color: context.colorScheme.mainPrimary,
                                      fontSize: context.isTablet ? 16 : 14.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    decoration: null,
                                  ),
                                ),
                              ),
                              ValueListenableBuilder(
                                  valueListenable: _searchController,
                                  builder: (context, value, child) {
                                    return value.text.isEmpty
                                        ? const SizedBox.shrink()
                                        : GestureDetector(
                                            onTap: () {
                                              _searchController.clear();
                                              setState(
                                                  () => _filterLanguages(''));
                                            },
                                            child: SvgPicture.asset(
                                              Assets.icons.icCloseWhite,
                                              width:
                                                  context.isTablet ? 16 : 16.w,
                                              height:
                                                  context.isTablet ? 16 : 16.h,
                                            ),
                                          );
                                  }),
                            ],
                          ),
                        ),
                        // Language list
                        Expanded(
                          child: CupertinoScrollbar(
                            thumbVisibility: true,
                            controller: _scrollController,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(16.r),
                              child: ListView(
                                controller: _scrollController,
                                padding: EdgeInsets.zero,
                                children: [
                                  // Show device language first if available
                                  if (_deviceLanguage !=
                                      const Language('err', 'Not found!'))
                                    _buildMenuItem(
                                      _deviceLanguage!,
                                      '${_deviceLanguage!.name} (Device)',
                                    ),
                                  // Show filtered languages
                                  ..._getFilteredLanguages()
                                      .where((lang) =>
                                          lang.code != _deviceLanguage?.code)
                                      .map((language) => _buildMenuItem(
                                          language, language.name)),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem(Language language, String text) {
    bool isSelected = language == _selectedLanguage;

    // Use 'Auto' text for auto language in webLink case
    String displayText = text;

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (!mounted) return;
        setState(() {
          _selectedLanguage = language;
          _isShowingMenu = false;
          _searchController.clear();
        });
        widget.onChanged(_selectedLanguage);

        if (!(language.code == 'auto' &&
            widget.from == LanguageDropdownFrom.webLink)) {
          _localService.saveSelectedLanguage(
              language.code, widget.from.toString());
        }

        _logEvents(widget.from);

        _hideLanguageMenu();
      },
      child: Container(
        width: double.infinity,
        height: context.isTablet ? 44 : 44.h,
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
        decoration: BoxDecoration(
          color: isSelected
              ? context.colorScheme.mainSecondary
              : context.colorScheme.mainNeutral,
        ),
        child: Row(
          children: [
            Expanded(
              child: CommonText(
                displayText,
                style: TextStyle(
                  fontSize: context.isTablet ? 16 : 14.sp,
                  fontWeight: FontWeight.w500,
                  color: context.colorScheme.mainPrimary,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                size: 24,
                color: context.colorScheme.mainBlue,
              ),
          ],
        ),
      ),
    );
  }

  void _logEvents(LanguageDropdownFrom from) {
    // Only log events when user manually selects a language
    // This is called from _buildMenuItem's onTap
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: from.eventName ?? 'unknown_language_selected',
    );
  }

  void _filterLanguages(String query) {
    if (query.isEmpty) {
      _filteredLanguages = _supportedLanguages;
    } else {
      _filteredLanguages = _supportedLanguages
          .where((language) =>
              language.name.toLowerCase().contains(query.toLowerCase()) ||
              language.code.toLowerCase().contains(query.toLowerCase()))
          .toList();
    }
  }

  List<Language> _getFilteredLanguages() {
    return _searchController.text.isEmpty
        ? _supportedLanguages
        : _filteredLanguages;
  }

  @override
  void dispose() {
    _searchController.clear(); // Clear search bar
    _searchController.dispose();
    _focusNode.dispose();
    _hideLanguageMenu();
    _scrollController.dispose();
    _overlayEntry?.remove();
    super.dispose();
  }
}

class Language {
  final String code;
  final String name;

  const Language(this.code, this.name);
}

const List<Language> supportedLanguages = [
  Language('en', 'English'),
  Language('fr', 'French (Français)'),
  Language('fr-CA', 'French Canadian (Français Canada)'),
  Language('es', 'Spanish (Español)'),
  Language('de', 'German (Deutsch)'),
  Language('ja', 'Japanese (日本語)'),
  Language('ko', 'Korean (한국어)'),
  Language('auto', 'Multi-language'),
  // Rest of the languages in alphabetical order by native name
  Language('ar', 'Arabic (العربية)'),
  Language('bg', 'Bulgarian (български)'),
  Language('ca', 'Catalan (Català)'),
  Language('cs', 'Czech (Čeština)'),
  Language('da', 'Danish (Dansk)'),
  Language('de-CH', 'Swiss German (Schweizerdeutsch)'),
  Language('el', 'Greek (Ελληνικά)'),
  Language('en-AU', 'English (Australia)'),
  Language('en-GB', 'English (UK)'),
  Language('en-IN', 'English (India)'),
  Language('en-NZ', 'English (New Zealand)'),
  Language('en-US', 'English (US)'),
  Language('es-419', 'Español Latinoamérica'),
  Language('et', 'Estonian (Eesti)'),
  Language('fi', 'Finnish (Suomi)'),
  Language('hi', 'Hindi (हिन्दी)'),
  Language('he', 'Hebrew (עברית)'),
  Language('hu', 'Hungarian (Magyar)'),
  Language('id', 'Indonesian (Bahasa Indonesia)'),
  Language('it', 'Italian (Italiano)'),
  Language('lt', 'Lithuanian (Lietuvių)'),
  Language('lv', 'Latvian (Latviešu)'),
  Language('ms', 'Malay (Bahasa Melayu)'),
  Language('multi', 'Spanish + English (Español + English)'),
  Language('nl', 'Dutch (Nederlands)'),
  Language('nl-BE', 'Flemish (Vlaams)'),
  Language('no', 'Norwegian (Norsk)'),
  Language('pl', 'Polish (Polski)'),
  Language('pt', 'Portuguese (Português)'),
  Language('pt-BR', 'Brazilian Portuguese (Português Brasil)'),
  Language('ro', 'Romanian (Română)'),
  Language('ru', 'Russian (Русский)'),
  Language('sk', 'Slovak (Slovenčina)'),
  Language('sv', 'Swedish (Svenska)'),
  Language('th', 'Thai (ไทย)'),
  Language('tr', 'Turkish (Türkçe)'),
  Language('uk', 'Ukrainian (Українська)'),
  Language('vi', 'Vietnamese (Tiếng Việt)'),
  Language('zh', 'Chinese (中文)'),
  Language('zh-HK', 'Cantonese (廣東話)'),
  Language('zh-TW', 'Traditional Chinese (繁體中文)'),
];

const List<Language> translateLanguages = [
  Language('ab', 'Abkhazian (аҧсуа бызшәа)'),
  Language('af', 'Afrikaans (Afrikaans)'),
  Language('ak', 'Akan (Twi)'),
  Language('am', 'Amharic (አማርኛ)'),
  Language('ar', 'Arabic (العربية)'),
  Language('as', 'Assamese (অসমীয়া)'),
  Language('ay', 'Aymara (Aymar aru)'),
  Language('az', 'Azerbaijani (Azərbaycan)'),
  Language('ba', 'Bashkir (башҡорт теле)'),
  Language('be', 'Belarusian (беларуская мова)'),
  Language('bg', 'Bulgarian (български)'),
  Language('bm', 'Bambara (Bamanankan)'),
  Language('bn', 'Bengali (বাংলা)'),
  Language('br', 'Breton (Brezhoneg)'),
  Language('bs', 'Bosnian (Bosanski)'),
  Language('ca', 'Catalan (Català)'),
  Language('co', 'Corsican (Corsu)'),
  Language('cs', 'Czech (Čeština)'),
  Language('cy', 'Welsh (Cymraeg)'),
  Language('da', 'Danish (Dansk)'),
  Language('de', 'German (Deutsch)'),
  Language('doi', 'Dogri (डोगरी)'),
  Language('dz', 'Dzongkha (རྫོང་ཁ)'),
  Language('ee', 'Ewe (Eʋegbe)'),
  Language('el', 'Greek (Ελληνικά)'),
  Language('en', 'English (English)'),
  Language('eo', 'Esperanto (Esperanto)'),
  Language('es', 'Spanish (Español)'),
  Language('et', 'Estonian (Eesti)'),
  Language('eu', 'Basque (Euskara)'),
  Language('fa', 'Persian (فارسی)'),
  Language('ff', 'Fulah (Fulfulde)'),
  Language('fi', 'Finnish (Suomi)'),
  Language('fil', 'Filipino (Filipino)'),
  Language('fj', 'Fijian (Na Vosa Vakaviti)'),
  Language('fr', 'French (Français)'),
  Language('fr-CA', 'French (Canada) (Français canadien)'),
  Language('fr-FR', 'French (France) (Français de France)'),
  Language('fy', 'Western Frisian (Frysk)'),
  Language('ga', 'Irish (Gaeilge)'),
  Language('gd', 'Scottish Gaelic (Gàidhlig)'),
  Language('gl', 'Galician (Galego)'),
  Language('gn', "Guarani (Avañe'ẽ)"),
  Language('gu', 'Gujarati (ગુજરાતી)'),
  Language('ha', 'Hausa (Hausa)'),
  Language('haw', 'Hawaiian (ʻŌlelo Hawaiʻi)'),
  Language('he', 'Hebrew (עברית)'),
  Language('hi', 'Hindi (हिन्दी)'),
  Language('hr', 'Croatian (Hrvatski)'),
  Language('ht', 'Haitian (Kreyòl ayisyen)'),
  Language('hu', 'Hungarian (Magyar)'),
  Language('hy', 'Armenian (Հայերեն)'),
  Language('id', 'Indonesian (Bahasa Indonesia)'),
  Language('ig', 'Igbo (Igbo)'),
  Language('is', 'Icelandic (Íslenska)'),
  Language('it', 'Italian (Italiano)'),
  Language('iw', 'Hebrew (עברית)'),
  Language('ja', 'Japanese (日本語)'),
  Language('jv', 'Javanese (Basa Jawa)'),
  Language('ka', 'Georgian (ქართული)'),
  Language('kk', 'Kazakh (қазақ тілі)'),
  Language('km', 'Khmer (ខ្មែរ)'),
  Language('kn', 'Kannada (ಕನ್ನಡ)'),
  Language('ko', 'Korean (한국어)'),
  Language('ku', 'Kurdish (Kurdî)'),
  Language('ky', 'Kyrgyz (кыргыз тили)'),
  Language('la', 'Latin (Latina)'),
  Language('lb', 'Luxembourgish (Lëtzebuergesch)'),
  Language('lg', 'Ganda (Luganda)'),
  Language('ln', 'Lingala (Lingála)'),
  Language('lo', 'Lao (ລາວ)'),
  Language('lt', 'Lithuanian (Lietuvių)'),
  Language('luo', 'Luo (Dholuo)'),
  Language('lus', 'Mizo (Mizo ṭawng)'),
  Language('lv', 'Latvian (Latviešu)'),
  Language('mai', 'Maithili (मैथिली)'),
  Language('mg', 'Malagasy (Malagasy)'),
  Language('mi', 'Māori (Te Reo Māori)'),
  Language('mk', 'Macedonian (македонски)'),
  Language('ml', 'Malayalam (മലയാളം)'),
  Language('mn', 'Mongolian (монгол)'),
  Language('mr', 'Marathi (मराठी)'),
  Language('ms', 'Malay (Bahasa Melayu)'),
  Language('mt', 'Maltese (Malti)'),
  Language('my', 'Burmese (မြန်မာစာ)'),
  Language('ne', 'Nepali (नेपाली)'),
  Language('nl', 'Dutch (Nederlands)'),
  Language('no', 'Norwegian (Norsk)'),
  Language('nr', 'Southern Ndebele (isiNdebele)'),
  Language('nso', 'Northern Sotho (Sesotho sa Leboa)'),
  Language('oc', 'Occitan (Occitan)'),
  Language('om', 'Oromo (Afaan Oromoo)'),
  Language('or', 'Odia (ଓଡ଼ିଆ)'),
  Language('pa', 'Punjabi (ਪੰਜਾਬੀ)'),
  Language('pap', 'Papiamento (Papiamento)'),
  Language('pl', 'Polish (Polski)'),
  Language('ps', 'Pashto (پښتو)'),
  Language('pt', 'Portuguese (Português)'),
  Language('pt-BR', 'Portuguese (Brazil) (Português do Brasil)'),
  Language('pt-PT', 'Portuguese (Portugal) (Português de Portugal)'),
  Language('qu', 'Quechua (Runa Simi)'),
  Language('rn', 'Rundi (Kirundi)'),
  Language('ro', 'Romanian (Română)'),
  Language('ru', 'Russian (русский)'),
  Language('rw', 'Kinyarwanda (Kinyarwanda)'),
  Language('sa', 'Sanskrit (संस्कृत)'),
  Language('sd', 'Sindhi (سنڌي)'),
  Language('sg', 'Sango (Sängö)'),
  Language('si', 'Sinhala (සිංහල)'),
  Language('sk', 'Slovak (Slovenčina)'),
  Language('sl', 'Slovenian (Slovenščina)'),
  Language('sm', "Samoan (Gagana fa'a Sāmoa)"),
  Language('sn', 'Shona (chiShona)'),
  Language('so', 'Somali (Soomaali)'),
  Language('sq', 'Albanian (Shqip)'),
  Language('sr', 'Serbian (српски)'),
  Language('ss', 'Swati (siSwati)'),
  Language('st', 'Southern Sotho (Sesotho)'),
  Language('su', 'Sundanese (Basa Sunda)'),
  Language('sv', 'Swedish (Svenska)'),
  Language('sw', 'Swahili (Kiswahili)'),
  Language('ta', 'Tamil (தமிழ்)'),
  Language('te', 'Telugu (తెలుగు)'),
  Language('tg', 'Tajik (тоҷикӣ)'),
  Language('th', 'Thai (ไทย)'),
  Language('ti', 'Tigrinya (ትግርኛ)'),
  Language('tk', 'Turkmen (Türkmençe)'),
  Language('tl', 'Tagalog (Tagalog)'),
  Language('tn', 'Tswana (Setswana)'),
  Language('tr', 'Turkish (Türkçe)'),
  Language('ts', 'Tsonga (Xitsonga)'),
  Language('tt', 'Tatar (татар теле)'),
  Language('ug', 'Uyghur (ئۇيغۇرچە)'),
  Language('uk', 'Ukrainian (українська)'),
  Language('ur', 'Urdu (اردو)'),
  Language('uz', "Uzbek (o'zbek)"),
  Language('vi', 'Vietnamese (Tiếng Việt)'),
  Language('xh', 'Xhosa (isiXhosa)'),
  Language('yi', 'Yiddish (ייִדיש)'),
  Language('yo', 'Yoruba (Yorùbá)'),
  Language('yue', 'Cantonese (廣東話)'),
  Language('zh-Hans', 'Chinese (Simplified) (简体中文)'),
  Language('zh-Hant', 'Chinese (Traditional) (繁體中文)'),
  Language('zh-CN', 'Chinese (China) (中文 (中国))'),
  Language('zh-TW', 'Chinese (Taiwan) (中文 (台灣))'),
  Language('zh-HK', 'Chinese (Hong Kong) (中文 (香港))'),
  Language('zu', 'Zulu (isiZulu)'),
];

enum LanguageDropdownUseCase {
  recordAndAudioFile,
  translateLanguageWithAuto,
  defaultTranslateLanguage,
}

enum LanguageDropdownFrom {
  record,
  audioFile,
  translate,
  webLink,
  document,
  text,
  image,
  camera;

  String? get eventName {
    switch (this) {
      case LanguageDropdownFrom.record:
        return EventName.record_choose_a_language;
      case LanguageDropdownFrom.audioFile:
        return EventName.upload_audio_choose_a_language;
      case LanguageDropdownFrom.translate:
        return EventName.translate_choose_a_language;
      case LanguageDropdownFrom.webLink:
        return EventName.weblink_choose_a_language;
      case LanguageDropdownFrom.document:
        return EventName.document_choose_a_language;
      case LanguageDropdownFrom.text:
        return EventName.text_choose_a_language;
      case LanguageDropdownFrom.image:
        return EventName.uploadImageLanguageSelected;
      case LanguageDropdownFrom.camera:
        return EventName.cameraLanguageSelected;
      default:
        return EventName.unknown_choose_a_language;
    }
  }
}
