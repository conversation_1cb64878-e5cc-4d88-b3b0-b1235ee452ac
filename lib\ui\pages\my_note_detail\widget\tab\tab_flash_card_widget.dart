import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:note_x/lib.dart';

class TabFlashCardWidget extends StatelessWidget {
  const TabFlashCardWidget({
    super.key,
    required this.widget,
    required this.cubit,
  });

  final MyNoteDetailPage widget;
  final MyNoteDetailCubit cubit;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: HiveService().noteBox.listenable(),
      builder: (context, box, child) {
        final currentNote = box.get(widget.noteModel.id) ?? widget.noteModel;
        return currentNote.flashCard.isNotEmpty
            ? FlashCardPage(
                flashcards: currentNote.flashCard,
                title: currentNote.title,
                audioFilePath: currentNote.audioFilePath,
                audioUrl: currentNote.audioUrl,
                isCommunityNote: widget.isCommunityNote,
                isGeneratingFlashCard: currentNote.isGeneratingFlashCard,
              )
            : EmptyPage(
                image: Assets.images.imgDetailFlashcard,
                title: S.current.click_start_flashcard,
                noteModel: currentNote,
                isLoading: currentNote.noteStatus == NoteStatus.loading,
                contentButton: currentNote.isGeneratingFlashCard
                    ? CupertinoActivityIndicator(
                        radius: 12.r,
                        color: context.colorScheme.themeWhite,
                      )
                    : Text(
                        S.current.content_button_flashcard,
                        style: TextStyle(
                          fontSize: context.isTablet ? 16 : 14.sp,
                          fontWeight: context.isTablet
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: currentNote.noteStatus == NoteStatus.error ||
                                  currentNote.noteStatus == NoteStatus.loading
                              ? context.colorScheme.mainPrimary
                                  .withOpacity(0.38)
                              : context.colorScheme.themeWhite,
                        ),
                      ),
                onTap: () {
                  if (!currentNote.isGeneratingFlashCard) {
                    _handleGenerateFlashcard(context);
                  }
                },
              );
      },
    );
  }

  void _handleGenerateFlashcard(BuildContext context) {
    cubit.appCubit.isUserProOrProlite()
        ? {
            AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.flashcard_sets_scr_create_clicked,
            ),
            showCreateQuizFlashcardBottomSheet(
              context: context,
              isCreateQuiz: false,
              title: S.current.settings,
              onSubmit: (cardCount, difficulty, topic) {
                cubit.onFlashCard(
                  communityNote: widget.isCommunityNote,
                  customCardCount: cardCount,
                  customDifficulty: difficulty,
                  customTopic: topic,
                );
                Navigator.of(context).pop();
              },
            )
          }
        : Navigator.of(context)
            .push(
              CupertinoPageRoute(
                builder: (context) => const PurchasePage(
                  from: PurchasePageFrom.iapCreateNote,
                ),
              ),
            )
            .then((didPurchaseSuccess) => {
                  if (didPurchaseSuccess == true)
                    {cubit.onFlashCard(communityNote: widget.isCommunityNote)}
                });
  }
}
