// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'folder_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

FolderDto _$FolderDtoFromJson(Map<String, dynamic> json) {
  return _FolderDto.fromJson(json);
}

/// @nodoc
mixin _$FolderDto {
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'folder_name')
  String get folderName => throw _privateConstructorUsedError;
  String get user => throw _privateConstructorUsedError;
  String get createdAt => throw _privateConstructorUsedError;
  String get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FolderDtoCopyWith<FolderDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FolderDtoCopyWith<$Res> {
  factory $FolderDtoCopyWith(FolderDto value, $Res Function(FolderDto) then) =
      _$FolderDtoCopyWithImpl<$Res, FolderDto>;
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'folder_name') String folderName,
      String user,
      String createdAt,
      String updatedAt});
}

/// @nodoc
class _$FolderDtoCopyWithImpl<$Res, $Val extends FolderDto>
    implements $FolderDtoCopyWith<$Res> {
  _$FolderDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? folderName = null,
    Object? user = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      folderName: null == folderName
          ? _value.folderName
          : folderName // ignore: cast_nullable_to_non_nullable
              as String,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FolderDtoImplCopyWith<$Res>
    implements $FolderDtoCopyWith<$Res> {
  factory _$$FolderDtoImplCopyWith(
          _$FolderDtoImpl value, $Res Function(_$FolderDtoImpl) then) =
      __$$FolderDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'folder_name') String folderName,
      String user,
      String createdAt,
      String updatedAt});
}

/// @nodoc
class __$$FolderDtoImplCopyWithImpl<$Res>
    extends _$FolderDtoCopyWithImpl<$Res, _$FolderDtoImpl>
    implements _$$FolderDtoImplCopyWith<$Res> {
  __$$FolderDtoImplCopyWithImpl(
      _$FolderDtoImpl _value, $Res Function(_$FolderDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? folderName = null,
    Object? user = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$FolderDtoImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      folderName: null == folderName
          ? _value.folderName
          : folderName // ignore: cast_nullable_to_non_nullable
              as String,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FolderDtoImpl implements _FolderDto {
  const _$FolderDtoImpl(
      {this.id = '',
      @JsonKey(name: 'folder_name') this.folderName = '',
      this.user = '',
      this.createdAt = '',
      this.updatedAt = ''});

  factory _$FolderDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$FolderDtoImplFromJson(json);

  @override
  @JsonKey()
  final String id;
  @override
  @JsonKey(name: 'folder_name')
  final String folderName;
  @override
  @JsonKey()
  final String user;
  @override
  @JsonKey()
  final String createdAt;
  @override
  @JsonKey()
  final String updatedAt;

  @override
  String toString() {
    return 'FolderDto(id: $id, folderName: $folderName, user: $user, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FolderDtoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.folderName, folderName) ||
                other.folderName == folderName) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, folderName, user, createdAt, updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FolderDtoImplCopyWith<_$FolderDtoImpl> get copyWith =>
      __$$FolderDtoImplCopyWithImpl<_$FolderDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FolderDtoImplToJson(
      this,
    );
  }
}

abstract class _FolderDto implements FolderDto {
  const factory _FolderDto(
      {final String id,
      @JsonKey(name: 'folder_name') final String folderName,
      final String user,
      final String createdAt,
      final String updatedAt}) = _$FolderDtoImpl;

  factory _FolderDto.fromJson(Map<String, dynamic> json) =
      _$FolderDtoImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(name: 'folder_name')
  String get folderName;
  @override
  String get user;
  @override
  String get createdAt;
  @override
  String get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$FolderDtoImplCopyWith<_$FolderDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
