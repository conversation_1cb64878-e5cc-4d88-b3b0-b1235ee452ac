import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:note_x/lib.dart';

class TabShortsWidget extends StatelessWidget {
  const TabShortsWidget({
    super.key,
    required this.noteModel,
  });

  final NoteModel noteModel;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: HiveService().noteBox.listenable(),
      builder: (context, box, child) {
        final currentNote = box.get(noteModel.id) ?? noteModel;

        return EmptyPage(
          image: Assets.images.imgDetailShort,
          title: S.current.click_create_short,
          noteModel: currentNote,
          isLoading: currentNote.noteStatus == NoteStatus.loading,
          contentButton: Text(
            S.current.create_short,
            style: TextStyle(
              fontSize: context.isTablet ? 16 : 14.sp,
              fontWeight: context.isTablet ? FontWeight.w600 : FontWeight.w500,
              color: currentNote.noteStatus == NoteStatus.error ||
                      currentNote.noteStatus == NoteStatus.loading
                  ? context.colorScheme.mainPrimary.withOpacity(0.38)
                  : context.colorScheme.themeWhite,
            ),
          ),
          onTap: () {
            AnalyticsService.logAnalyticsEventNoParam(
                eventName: EventName.shorts_scr_create_clicked);
            if (currentNote.noteStatus != NoteStatus.loading &&
                currentNote.noteStatus != NoteStatus.error) {
              CreateShortsBottomSheet.show(
                context,
                currentNote,
                CreateShortsType.video,
              );
            }
          },
        );
      },
    );
  }
}
