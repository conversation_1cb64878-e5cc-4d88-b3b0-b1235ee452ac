import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

/// A common widget for exporting notes in different formats
///
/// This widget displays a modal bottom sheet that allows users to select
/// an export format for a note.
class ExportNoteHelper {
  /// The local service for getting set information
  static final _localService = GetIt.instance.get<LocalService>();

  /// The export service for handling export operations
  static final _exportService = ExportService();

  /// Shows a modal bottom sheet for selecting an export format
  ///
  /// [context] - The build context
  /// [exportType] - The type of export (quiz, flashcards, etc.)
  /// [note] - The note to be exported
  static void showBottomSheet(
    BuildContext context,
    ExportType exportType,
    NoteModel note,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: context.colorScheme.mainNeutral,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.85,
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonText(
                      exportType.displayTitle,
                      style: TextStyle(
                        color: context.colorScheme.mainPrimary,
                        fontWeight: FontWeight.w600,
                        fontSize: context.isTablet ? 22 : 20.sp,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: SvgPicture.asset(
                        Assets.icons.icCloseWhite,
                        width: 24.w,
                        height: 24.h,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainGray,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: exportType.getExportFormatList(note.type).length,
                  itemBuilder: (context, index) {
                    final format =
                        exportType.getExportFormatList(note.type)[index];
                    return Column(
                      children: [
                        ExportOptionItem(
                          text: format.displayName,
                          onTap: () {
                            Navigator.pop(context);
                            _handleExport(context, exportType, format, note);
                          },
                        ),
                        if (format != exportType.exportFormatList.last)
                          AppConstants.kSpacingItem12,
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
    );
  }

  /// Handles the export operation based on the export type and format
  ///
  /// [context] - The build context
  /// [exportType] - The type of export (quiz, flashcards, etc.)
  /// [format] - The format to export in (PDF, DOCX, etc.)
  /// [note] - The note to be exported
  static void _handleExport(
    BuildContext context,
    ExportType exportType,
    ExportNoteFormat format,
    NoteModel note,
  ) {
    // Get share position for tablets
    final Rect? sharePosition = context.isTablet
        ? Rect.fromCenter(
            center: Offset(
              MediaQuery.of(context).size.width / 2,
              MediaQuery.of(context).size.height / 2,
            ),
            width: 100,
            height: 100,
          )
        : null;

    if (exportType == ExportType.quiz) {
      final setInfoQuiz = _localService.getQuizSetInfo(note.backendNoteId);
      final String setId = setInfoQuiz['setId'] ?? '';
      final String title = setInfoQuiz['title'] ?? '';

      // Check and get setId from noteModel if needed
      final String finalSetId = setId.isEmpty ? 'backendNoteId' : setId;

      _exportService.exportQuizSet(
        format,
        sharePosition,
        title,
        finalSetId,
        note.backendNoteId,
      );
    } else if (exportType == ExportType.flashcards) {
      final setInfoFlashcard =
          _localService.getFlashcardSetInfo(note.backendNoteId);
      final String setId = setInfoFlashcard['setId'] ?? '';
      final String title = setInfoFlashcard['title'] ?? '';

      // Check and get setId from noteModel if needed
      final String finalSetId = setId.isEmpty ? 'backendNoteId' : setId;

      _exportService.exportFlashcardSet(
        format,
        sharePosition,
        title,
        finalSetId,
        note.backendNoteId,
      );
    } else {
      // Handle summary and transcript export
      _exportService.exportNote(
        format,
        exportType,
        sharePosition,
        note.backendNoteId,
        note.title,
      );
    }
  }
}
