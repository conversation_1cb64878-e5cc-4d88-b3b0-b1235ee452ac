import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';
import 'package:pull_down_button/pull_down_button.dart';

class ItemFlashcardStep extends StatefulWidget {
  final List<FlashcardSetDto>? flashcardSets;
  final VoidCallback? onRefresh;

  const ItemFlashcardStep({
    super.key,
    this.flashcardSets,
    this.onRefresh,
  });

  @override
  State<ItemFlashcardStep> createState() => _ItemFlashcardStepState();
}

class _ItemFlashcardStepState extends State<ItemFlashcardStep> {
  TextEditingController editNameController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    if (widget.flashcardSets == null || widget.flashcardSets!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: List.generate(widget.flashcardSets!.length, (index) {
        final flashcard = widget.flashcardSets![index];

        return GestureDetector(
          onTap: () {
            _chooseFlashcardSet(flashcard.setId, context);
          },
          child: Padding(
            padding: EdgeInsets.only(
                bottom: index < widget.flashcardSets!.length - 1 ? 12.h : 0),
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.r),
                color: context.colorScheme.mainNeutral,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonText(
                          flashcard.name,
                          style: TextStyle(
                            color: context.colorScheme.mainPrimary,
                            fontWeight: FontWeight.w500,
                            fontSize: context.isTablet ? 16 : 14.sp,
                          ),
                          maxLines: 2,
                        ),
                        AppConstants.kSpacingItem4,
                        CommonText(
                          MyUtils.formatDateTimes(flashcard.createdAt),
                          style: TextStyle(
                            fontSize: context.isTablet ? 15 : 12.sp,
                            fontWeight: FontWeight.w400,
                            color:
                                context.colorScheme.mainGray.withOpacity(0.6),
                          ),
                        ),
                        AppConstants.kSpacingItem8,
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 6.h,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primaryBlue.withOpacity(0.24),
                                borderRadius: BorderRadius.circular(100.r),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SvgPicture.asset(
                                    width: 11.w,
                                    height: 11.h,
                                    Assets.icons.icNumberQuestion,
                                    colorFilter: const ColorFilter.mode(
                                      AppColors.gradientCtaLightBlueStart,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                  AppConstants.kSpacingItemW4,
                                  CommonText(
                                    '${flashcard.cardsCount} ${flashcard.cardsCount == 1 ? 'Card' : 'Cards'}',
                                    height: 1,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontSize: context.isTablet ? 14 : 12.sp,
                                      color: AppColors.primaryBlue,
                                    ),
                                  )
                                ],
                              ),
                            ),
                            AppConstants.kSpacingItemW8,
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 6.h,
                              ),
                              decoration: BoxDecoration(
                                color: _getDifficultyBackgroundColor(
                                  flashcard.difficulty,
                                ),
                                borderRadius: BorderRadius.circular(100.r),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SvgPicture.asset(
                                    width: 11.w,
                                    height: 11.h,
                                    Assets.icons.icDifficulty,
                                    colorFilter: ColorFilter.mode(
                                      _getDifficultyColor(flashcard.difficulty),
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                  AppConstants.kSpacingItemW4,
                                  CommonText(
                                    MyUtils.formatDifficultyText(
                                        flashcard.difficulty),
                                    height: 1,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontSize: context.isTablet ? 14 : 12.sp,
                                      color: _getDifficultyColor(
                                          flashcard.difficulty),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  SetsOptions(
                    isInDetailPage: true,
                    editNameController: editNameController,
                    flashcardSet: flashcard,
                    onDeleteSuccess: widget.onRefresh,
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  void _chooseFlashcardSet(String setId, BuildContext context) async {
    final cubit = context.read<MyNoteDetailCubit>();
    final success = await cubit.chooseFlashcardSet(setId);
    if (success) {
      if (widget.onRefresh != null) {
        widget.onRefresh!();
      }
      // Pop về màn my_note_detail_page
      CommonDialogs.closeLoading();
      // ignore: use_build_context_synchronously
      Navigator.of(context).pop();
    }
  }

  // Thêm helper method để lấy màu
  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return AppColors.primaryGreen;
      case 'medium':
        return AppColors.primaryYellow;
      case 'hard':
        return AppColors.primaryRed;
      default:
        return AppColors.primaryBlue;
    }
  }

  // Thêm helper method để lấy opacity color
  Color _getDifficultyBackgroundColor(String difficulty) {
    return _getDifficultyColor(difficulty).withOpacity(0.24);
  }
}

class SetsOptions extends StatelessWidget {
  final TextEditingController editNameController;
  final bool isInDetailPage;
  final FlashcardSetDto? flashcardSet;
  final Function? onDeleteSuccess;

  const SetsOptions({
    Key? key,
    required this.editNameController,
    required this.isInDetailPage,
    this.flashcardSet,
    this.onDeleteSuccess,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<MyNoteDetailCubit>();

    return PullDownButton(
      routeTheme: PullDownMenuRouteTheme(
        backgroundColor: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(16.r),
        width: context.isTablet ? 200 : 200.w,
      ),
      itemBuilder: (context) => [
        PullDownMenuItem(
          title: S.current.edit_name,
          itemTheme: PullDownMenuItemTheme(
              textStyle: TextStyle(
            color: context.colorScheme.mainPrimary,
            fontSize: context.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w500,
          )),
          onTap: () async {
            if (flashcardSet != null) {
              editNameController.text = flashcardSet!.name;
              showEditNameDialog(
                context,
                controller: editNameController,
                onPressed: () async {
                  if (editNameController.text.trim().isNotEmpty) {
                    final success = await cubit.editFlashcardSet(
                        flashcardSet!.setId, editNameController.text.trim());
                    if (success) {
                      if (onDeleteSuccess != null) {
                        onDeleteSuccess!(); // Use the same callback for refresh
                      }
                      _editFlashcardSet();
                      log('Flashcard set name updated successfully');
                    } else {
                      log('Failed to update flashcard set name');
                    }
                  }
                },
                title: S.current.edit_name,
                contentButton: S.current.change,
                hintText: S.current.enter_new_name,
                initialValue: flashcardSet!.name,
              );
            }
          },
          iconWidget: SvgPicture.asset(
            Assets.icons.icEditReminderTitle,
            width: 18.w,
            height: 18.h,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
        ),
        PullDownMenuItem(
          title: S.current.export_flashcard,
          itemTheme: PullDownMenuItemTheme(
              textStyle: TextStyle(
            color: context.colorScheme.mainPrimary,
            fontSize: context.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w500,
          )),
          onTap: () async {
            showModalBottomSheetExport(
              context,
              ExportType.flashcards,
              Func1(
                (formatType) {
                  cubit.handleExportFlashcardSet(
                    formatType,
                    cubit.appCubit.isTablet
                        ? Rect.fromCenter(
                            center: Offset(
                              MediaQuery.of(context).size.width / 2,
                              MediaQuery.of(context).size.height / 2,
                            ),
                            width: 100,
                            height: 100,
                          )
                        : null,
                    flashcardSet?.name ?? '',
                    flashcardSet?.setId ?? '',
                    ExportType.flashcards,
                  );
                },
              ),
              cubit.loadingNoteStates,
            );
          },
          iconWidget: SvgPicture.asset(
            Assets.icons.icDownload,
            width: 18.w,
            height: 18.h,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
        ),
        PullDownMenuItem(
          itemTheme: PullDownMenuItemTheme(
              textStyle: TextStyle(
            color: context.colorScheme.themeWhite,
            fontSize: context.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w500,
          )),
          onTap: () {
            CommonDialogs.buildDeleteDialog(
              context,
              title: S.current.delete_this_item,
              content: S.current.you_will_not_be,
              headerImageAssetFile: Assets.icons.icMascottDelete,
              onPressedDeleteButton: () async {
                if (flashcardSet != null) {
                  final success =
                      await cubit.deleteFlashcardSet(flashcardSet!.setId);
                  if (success) {
                    if (onDeleteSuccess != null) {
                      onDeleteSuccess!();
                    }
                    _deleteFlashcardSet();
                    log('Flashcard set deleted successfully');
                  } else {
                    log('Failed to delete flashcard set');
                  }
                }
              },
              onPressedCancelButton: () {
                //No action needed
              },
            );
          },
          title: S.current.delete,
          isDestructive: true,
          iconWidget: SvgPicture.asset(
            Assets.icons.icDelete,
            colorFilter: const ColorFilter.mode(
              AppColors.primaryRed,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
      buttonBuilder: (context, showMenu) {
        return GestureDetector(
          onTap: () {
            showMenu();
          },
          child: SvgPicture.asset(
            Assets.icons.icMore,
            width: context.isTablet ? 24 : 24.w,
            height: context.isTablet ? 24 : 24.w,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
        );
      },
    );
  }

  void _deleteFlashcardSet() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_delete_flashcard_set,
    );
  }

  void _editFlashcardSet() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_edit_flashcard_set,
    );
  }
}
