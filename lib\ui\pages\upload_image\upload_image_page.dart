import 'dart:io';
import 'dart:async';

import 'package:el_tooltip/el_tooltip.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import '../../../base/base_page_state.dart';
import 'cubit/upload_image_state.dart';
import 'package:get_it/get_it.dart';

class UploadImagePage extends StatefulWidget {
  static const routeName = 'UploadImagePage';

  final VoidCallback? onClose;
  final VoidCallback? onCreateNoteSuccess;
  final bool isFromWelcome;

  const UploadImagePage({
    super.key,
    this.onClose,
    this.onCreateNoteSuccess,
    this.isFromWelcome = false,
  });

  @override
  State<UploadImagePage> createState() => _UploadImagePageState();
}

class _UploadImagePageState
    extends BasePageStateDelegate<UploadImagePage, UploadImageCubit> {
  late ScrollController _imageListScrollController;
  Timer? _animationTimer;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _imageListScrollController = ScrollController();
    cubit.logEventTrackingOpenImagePage();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      cubit.addImagesFromGallery();
    });
  }

  @override
  void dispose() {
    _imageListScrollController.dispose();
    _animationTimer?.cancel();
    _removeOverlay();
    super.dispose();
    widget.onClose?.call();
  }

  void _showAnimationOverlay() {
    _removeOverlay();

    final overlay = Overlay.of(context);
    final appBarHeight = AppBar().preferredSize.height - 25.h;

    _overlayEntry = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black.withOpacity(0.5),
        child: Stack(
          children: [
            Positioned(
              top: -appBarHeight,
              left: 0,
              right: 0,
              child: Lottie.asset(
                Assets.videos.swapImage,
                repeat: true,
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                fit: BoxFit.contain,
              ),
            ),
          ],
        ),
      ),
    );

    overlay.insert(_overlayEntry!);

    _animationTimer?.cancel();
    _animationTimer = Timer(const Duration(seconds: 3), () {
      _removeOverlay();
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return MultiBlocListener(
      listeners: [
        BlocListener<UploadImageCubit, UploadImageState>(
          listenWhen: (previous, current) =>
              previous.selectedImagePaths != current.selectedImagePaths &&
              previous.selectedImagePaths.length <
                  current.selectedImagePaths.length,
          listener: (context, state) {
            if (state.selectedImagePaths.isNotEmpty) {
              _scrollToLastImage();
            }
          },
        ),

        // Handle loading dialog for image merging
        BlocListener<UploadImageCubit, UploadImageState>(
          listenWhen: (previous, current) =>
              previous.isMergingImages != current.isMergingImages,
          listener: (context, state) {
            if (state.isMergingImages) {
              CommonDialogs.showLoadingDialog(
                dialogText: ValueNotifier(S.current.processing_image),
              );
            } else if (CommonDialogs.isLoadingDialogOpen) {
              CommonDialogs.closeLoading();
            }
          },
        ),

        // Handle one-shot events
        BlocListener<UploadImageCubit, UploadImageState>(
          listenWhen: (previous, current) =>
              previous.oneShotEvent != current.oneShotEvent &&
              current.oneShotEvent != CreateNoteWithImageOneShotEvent.none,
          listener: (context, state) async {
            _handleOneShotEvent(context, state.oneShotEvent);
            cubit.resetEnumState();
          },
        ),

        // Handle scroll to last image event
        BlocListener<UploadImageCubit, UploadImageState>(
          listenWhen: (previous, current) =>
              current.oneShotEvent ==
              CreateNoteWithImageOneShotEvent.scrollToLastImage,
          listener: (context, state) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _scrollToLastImage();
            });
            cubit.resetEnumState();
          },
        ),

        // Show animation when new images are added
        BlocListener<UploadImageCubit, UploadImageState>(
          listenWhen: (previous, current) =>
              previous.selectedImagePaths != current.selectedImagePaths &&
              current.selectedImagePaths.isNotEmpty &&
              previous.selectedImagePaths.length <
                  current.selectedImagePaths.length,
          listener: (context, state) {
            _showAnimationOverlay();
          },
        ),
      ],
      child: child,
    );
  }

  void _handleOneShotEvent(
      BuildContext context, CreateNoteWithImageOneShotEvent event) async {
    switch (event) {
      case CreateNoteWithImageOneShotEvent.mergePdfCompleted:
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => ProgressPage(
              note: cubit.getImageNote(),
              onRetry: () => cubit.onSubmitImages(),
            ),
          ),
        );
        break;

      case CreateNoteWithImageOneShotEvent.createNoteSuccessfully:
        await _handleNoteCreationSuccess();
        break;

      case CreateNoteWithImageOneShotEvent.onShowIAPFromImage:
        _showPurchasePage();
        break;

      case CreateNoteWithImageOneShotEvent.maxImagesReached:
        CommonDialogs.showToast(S.current.support_for_up_to_10_images);
        break;

      case CreateNoteWithImageOneShotEvent.mergePdfFailed:
        CommonDialogs.showToast(S.current.cannot_create_pdf_file_from_image);
        break;

      default:
        break;
    }
  }

  Future<void> _handleNoteCreationSuccess() async {
    final noteModel = cubit.getImageNote();
    cubit.resetState();

    widget.onClose?.call();
    widget.onCreateNoteSuccess?.call();

    Navigator.of(context).pop();
    final savedTabs =
        await GetIt.instance.get<LocalService>().loadSelectedItems();
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MyNoteDetailPage(
          noteModel: noteModel,
          isTablet: cubit.appCubit.isTablet,
          from: NoteDetailPageFrom.imageScreen,
          savedTabs: savedTabs,
        ),
      ),
    );
  }

  void _showPurchasePage() {
    Navigator.of(context).pushNamed(
      PurchasePage.routeName,
      arguments: {EventKey.from: PurchasePageFrom.iapCreateNote},
    ).then((didPurchaseSuccess) {
      if (didPurchaseSuccess == true) {
        cubit.onSubmitImages();
      }
    });
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBarWidget(
        isShowLeftButton: true,
        title: S.current.upload_image,
        onPressed: _onBackPressed,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                margin: EdgeInsets.symmetric(
                  horizontal: context.isTablet ? 16 : 16.w,
                  vertical: context.isTablet ? 16 : 16.h,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildUploadHeader(),
                    AppConstants.kSpacingItem8,
                    _buildSupportText(),
                    _buildImagePickerSection(context),
                    _buildFolderSection(),
                    AppConstants.kSpacingItem16,
                    _buildLanguageSection(),
                    AppConstants.kSpacingItem16,
                    AdvancedWidget(
                      onSummaryStyleChanged: cubit.updateSummaryStyle,
                      onWritingStyleChanged: cubit.updateWritingStyle,
                      onAdditionalInstructionsChanged:
                          cubit.updateAdditionalInstructions,
                      onAdvancedToggled: cubit.setAdvancedEnabled,
                    ),
                  ],
                ),
              ),
            ),
          ),
          _buildSubmitButton(),
        ],
      ),
    );
  }

  Widget _buildUploadHeader() {
    return BlocBuilder<UploadImageCubit, UploadImageState>(
      buildWhen: (previous, current) =>
          previous.selectedImagePaths != current.selectedImagePaths,
      builder: (context, state) {
        final images = state.selectedImagePaths;
        return images.isEmpty
            ? _buildUploadButton()
            : _buildUploadStatusBar(images.length);
      },
    );
  }

  Widget _buildUploadButton() {
    return Container(
      height: context.isLandscape
          ? 75
          : cubit.appCubit.isTablet
              ? 56
              : 56.h,
      padding: cubit.appCubit.isTablet
          ? const EdgeInsets.symmetric(horizontal: 11, vertical: 8)
          : EdgeInsets.symmetric(horizontal: 11.w, vertical: 8.h),
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: AppColors.gradientCTABlue,
          begin: Alignment.centerRight,
          end: Alignment.centerLeft,
        ),
        borderRadius: cubit.appCubit.isTablet
            ? BorderRadius.circular(54)
            : BorderRadius.circular(54.r),
      ),
      child: InkWell(
        onTap: () => cubit.addImagesFromGallery(),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              Assets.icons.icUploadFile,
              colorFilter: const ColorFilter.mode(
                AppColors.white,
                BlendMode.srcIn,
              ),
              width: cubit.appCubit.isTablet ? 24 : 24.w,
              height: cubit.appCubit.isTablet ? 24 : 24.w,
            ),
            AppConstants.kSpacingItemW4,
            Padding(
              padding: EdgeInsets.symmetric(vertical: 4.w),
              child: CommonText(
                S.current.upload_image,
                style: TextStyle(
                  fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                  color: AppColors.white,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadStatusBar(int imageCount) {
    return Container(
      height: context.isLandscape
          ? 75
          : cubit.appCubit.isTablet
              ? 56
              : 56.h,
      width: double.infinity,
      decoration: BoxDecoration(
        color: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(64.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              margin: context.isTablet
                  ? const EdgeInsets.symmetric(horizontal: 16, vertical: 16)
                  : EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              height: 32.h,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  CommonText(
                    S.current.images_have_been_uploaded(
                      imageCount.toString(),
                    ),
                    style: TextStyle(
                      fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                      fontWeight: FontWeight.w500,
                      color: context.colorScheme.mainPrimary,
                    ),
                  ),
                ],
              ),
            ),
          ),
          IconButton(
            onPressed: () {
              showNewCupertinoDialog(
                context: context,
                title: S.current.discard_changes,
                message: S.current.content_discard_changes_image,
                image: Assets.icons.icDiscardChanges,
                cancelButton: S.current.cancel,
                confirmButton: S.current.discard,
                onCancel: () {},
                onConfirm: () async {
                  cubit.clearImages();
                },
              );
            },
            icon: SvgPicture.asset(
              Assets.icons.icCloseWhite,
              width: 24.w,
              height: 24.h,
              colorFilter: ColorFilter.mode(
                context.colorScheme.mainGray,
                BlendMode.srcIn,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSupportText() {
    return Center(
      child: CommonText(
        S.current.support_image,
        style: TextStyle(
          fontSize: cubit.appCubit.isTablet ? 14 : 12.sp,
          fontWeight: FontWeight.w400,
          color: context.colorScheme.mainGray,
        ),
      ),
    );
  }

  Widget _buildFolderSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(
          S.current.folder,
          textColor: context.colorScheme.mainGray,
          style: TextStyle(
            fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
        AppConstants.kSpacingItem8,
        SizedBox(
          width: double.infinity,
          child: FolderDropdownView(
            folders: [
              FolderModel(
                  id: 'all_notes',
                  folderName: S.current.all_note,
                  backendId: ''),
              ...HiveFolderService.getAllFolders()
            ],
            selectedFolder: cubit.selectFolderNotifier,
            onMenuStateChanged: (isShowing) {},
            from: FolderDropdownFrom.image,
          ),
        ),
      ],
    );
  }

  Widget _buildLanguageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CommonText(
              S.current.summary_language,
              textColor: context.colorScheme.mainGray,
              style: TextStyle(
                fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
            AppConstants.kSpacingItemW4,
            ElTooltip(
              padding: EdgeInsets.all(8.w),
              content: const LanguageTipsWidget(
                isRecording: false,
                isCheckDocAndText: true,
              ),
              position: ElTooltipPosition.bottomCenter,
              color: context.colorScheme.mainBlue,
              radius: Radius.circular(16.r),
              child: SvgPicture.asset(
                Assets.icons.icCommonInfoTooltip,
                height: cubit.appCubit.isTablet ? 18 : 18.w,
                width: cubit.appCubit.isTablet ? 18 : 18.w,
                colorFilter: ColorFilter.mode(
                  context.colorScheme.mainPrimary,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ],
        ),
        AppConstants.kSpacingItem8,
        SizedBox(
          width: double.infinity,
          child: LanguageDropdownView(
            initialLanguageCode: cubit.targetImageLanguage?.code,
            onChanged: (lang) => {cubit.setImageLanguage(lang)},
            onMenuStateChanged: (isShowing) {
              if (isShowing) {}
            },
            useCase: LanguageDropdownUseCase.translateLanguageWithAuto,
            from: LanguageDropdownFrom.image,
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return Padding(
      padding: EdgeInsets.only(
          top: 16,
          bottom: MediaQuery.of(context).viewInsets.bottom > 0
              ? 16.h
              : context.isTablet
                  ? 32
                  : 32.h),
      child: BlocBuilder<UploadImageCubit, UploadImageState>(
        buildWhen: (previous, current) =>
            previous.selectedImagePaths != current.selectedImagePaths,
        builder: (context, state) {
          final hasImages = state.selectedImagePaths.isNotEmpty;
          return Center(
            child: AppCommonButton(
              width: cubit.appCubit.isTablet ? 200 : 160.w,
              height: cubit.appCubit.isTablet ? 44 : 44.h,
              borderRadius: BorderRadius.circular(24.r),
              backgroundColor: hasImages
                  ? context.colorScheme.mainBlue
                  : context.colorScheme.mainNeutral,
              textWidget: Text(
                S.current.add_to_notes,
                style: TextStyle(
                  fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                  fontWeight: FontWeight.w500,
                  color: hasImages
                      ? context.colorScheme.themeWhite
                      : context.colorScheme.mainPrimary.withOpacity(0.38),
                ),
              ),
              onPressed: hasImages
                  ? () {
                      cubit.focusNodeCreateNote.unfocus();
                      cubit.onSubmitImages();
                    }
                  : null,
            ),
          );
        },
      ),
    );
  }

  Widget _buildImagePickerSection(BuildContext context) {
    return BlocBuilder<UploadImageCubit, UploadImageState>(
      buildWhen: (prev, curr) =>
          prev.selectedImagePaths != curr.selectedImagePaths,
      builder: (context, state) {
        final images = state.selectedImagePaths;
        if (images.isEmpty) return const SizedBox.shrink();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            SizedBox(
              height: context.isTablet ? 60 : 60.h,
              child: Stack(
                children: [
                  Row(
                    children: [
                      // Add image button
                      GestureDetector(
                        onTap: () => cubit.addImagesFromGallery(),
                        child: Padding(
                          padding: EdgeInsets.only(
                              right: context.isTablet ? 8 : 8.w),
                          child: SvgPicture.asset(
                            context.colorScheme.mainPrimary == AppColors.white
                                ? Assets.icons.icAddImage
                                : Assets.icons.icAddImageLightMode,
                            width: context.isTablet ? 60 : 60.w,
                            height: context.isTablet ? 60 : 60.h,
                          ),
                        ),
                      ),

                      // Image list
                      Expanded(
                        child: ReorderableListView.builder(
                          scrollDirection: Axis.horizontal,
                          scrollController: _imageListScrollController,
                          itemCount: images.length,
                          onReorder: (oldIndex, newIndex) {
                            if (oldIndex < newIndex) newIndex -= 1;
                            cubit.swapImages(oldIndex, newIndex);
                          },
                          proxyDecorator: (child, index, animation) => Material(
                            elevation: 4.0 * animation.value,
                            color: Colors.transparent,
                            child: child,
                          ),
                          itemBuilder: (context, index) {
                            final imgPath = images[index];
                            return Stack(
                              key: ValueKey(imgPath),
                              children: [
                                // Image thumbnail
                                GestureDetector(
                                  onTap: () => _openImagePreview(images, index),
                                  child: Padding(
                                    padding: EdgeInsets.only(right: 8.w),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8.r),
                                      child: Image.file(
                                        File(imgPath),
                                        width: context.isTablet ? 60 : 60.w,
                                        height: context.isTablet ? 60 : 60.h,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                ),

                                // Delete button
                                Positioned(
                                  top: 2,
                                  right: 10,
                                  child: GestureDetector(
                                    onTap: () => cubit.removeImage(index),
                                    child: SvgPicture.asset(
                                      Assets.icons.icCloseBlack,
                                      width: context.isTablet ? 20 : 12.w,
                                      height: context.isTablet ? 20 : 12.h,
                                    ),
                                  ),
                                ),

                                // Image counter
                                Positioned(
                                  bottom: 0,
                                  left: 0,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 6, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: AppColors.primaryBlue
                                          .withOpacity(0.3),
                                      borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(8.r),
                                      ),
                                      boxShadow: const [
                                        BoxShadow(
                                          color: AppColors.primaryBlue,
                                          blurRadius: 30,
                                          spreadRadius: -10,
                                          blurStyle: BlurStyle.inner,
                                        ),
                                      ],
                                    ),
                                    child: Text(
                                      '${index + 1}',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: context.isTablet ? 12 : 10.sp,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            AppConstants.kSpacingItem16,
          ],
        );
      },
    );
  }

  void _scrollToLastImage() {
    if (!_imageListScrollController.hasClients) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _imageListScrollController.animateTo(
        _imageListScrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });
  }

  void _openImagePreview(List<String> images, int index) {
    cubit.logEventTrackingPreviewImage();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ImagePreviewGallery(
          imagePaths: images,
          initialIndex: index,
          onSwapImages: cubit.swapImages,
          onDeleteImage: cubit.removeImage,
          onRefresh: () => setState(() {}),
        ),
      ),
    );
  }

  void _onBackPressed() {
    if (cubit.hasUnsavedChanges) {
      showNewCupertinoDialog(
        context: context,
        title: S.current.discard_changes,
        message: S.current.content_discard_changes_image,
        image: Assets.icons.icDiscardChanges,
        cancelButton: S.current.cancel,
        confirmButton: S.current.discard,
        onCancel: () {},
        onConfirm: () {
          cubit.clearImages();
          _exitPage();
        },
      );
    } else {
      _exitPage();
    }
  }

  void _exitPage() {
    cubit.logEventTrackingImageBack();
    Navigator.of(context).pop();
  }
}
