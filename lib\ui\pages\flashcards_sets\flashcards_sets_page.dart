import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';
import '../../../base/base_page_state.dart';
import '../my_note_detail/cubit/my_note_detail_state.dart';

class FlashcardsSetsPage extends StatefulWidget {
  static const String routeName = 'FlashcardsSetsPage';
  final NoteModel noteModel;
  final bool isCommunityNote;
  final MyNoteDetailCubit parentCubit;

  const FlashcardsSetsPage(
      {super.key,
      required this.noteModel,
      required this.isCommunityNote,
      required this.parentCubit});

  @override
  FlashcardsSetsPageState createState() => FlashcardsSetsPageState();
}

class FlashcardsSetsPageState
    extends BasePageStateDelegate<FlashcardsSetsPage, MyNoteDetailCubit> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      cubit.getAllFlashcardSets(widget.noteModel.backendNoteId);
    });
  }

  @override
  MyNoteDetailCubit? get parentCubit => widget.parentCubit;

  void _refreshFlashcardSets() {
    cubit.getAllFlashcardSets(widget.noteModel.backendNoteId);
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<MyNoteDetailCubit, MyNoteDetailState>(
      listenWhen: (previous, current) =>
          previous.isGeneratingFlashCard != current.isGeneratingFlashCard ||
          previous.flashcardEvent != current.flashcardEvent,
      listener: (context, state) {
        // When flashcard generation starts, navigate back to MyNoteDetailPage
        if (state.isGeneratingFlashCard) {
          Navigator.of(context).pop({'selectFlashcardTab': true});
        }

        if (state.chooseFlashcard == ChooseFlashcardOneShotEvent.loading) {
          CommonDialogs.showLoadingDialog();
        } else if (state.chooseFlashcard ==
                ChooseFlashcardOneShotEvent.success ||
            state.chooseFlashcard == ChooseFlashcardOneShotEvent.error) {
          CommonDialogs.closeLoading(); // Dismiss loading dialog
        }
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
        appBar: AppBarWidget(
          isShowLeftButton: true,
          title: S.current.flashcard_sets,
          isCloseButton: true,
          onPressed: () {
            Navigator.of(context).pop();
            AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.flashcard_scr_back_clicked,
            );
          },
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              AppConstants.kSpacingItem8,
              BlocBuilder<MyNoteDetailCubit, MyNoteDetailState>(
                builder: (context, state) {
                  return AppCommonButton(
                    borderRadius: BorderRadius.circular(54.r),
                    margin: EdgeInsets.symmetric(
                      horizontal: 16.w,
                    ),
                    gradient: const LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: AppColors.gradientCTABlue,
                      transform: GradientRotation(pi),
                    ),
                    leftIcon: SvgPicture.asset(Assets.icons.icAdd),
                    textWidget: CommonText(
                      S.current.content_button_flashcard,
                      style: TextStyle(
                        fontSize: context.isTablet ? 18 : 16.sp,
                        fontWeight: FontWeight.w500,
                        color: context.colorScheme.themeWhite,
                        height: 1,
                      ),
                    ),
                    onPressed: () {
                      handleCreateFlashcards(
                        context,
                        cubit,
                        widget.noteModel,
                        widget.isCommunityNote,
                      );
                    },
                  );
                },
              ),
              AppConstants.kSpacingItem8,
              Center(
                child: CommonText(
                  S.current.max_3_flashcard_sets,
                  style: TextStyle(
                    fontSize: context.isTablet ? 14 : 12.sp,
                    fontWeight: FontWeight.w400,
                    color: context.colorScheme.mainGray,
                  ),
                ),
              ),
              AppConstants.kSpacingItem12,
              BlocBuilder<MyNoteDetailCubit, MyNoteDetailState>(
                buildWhen: (previous, current) =>
                    previous.flashcardEvent != current.flashcardEvent,
                builder: (context, state) {
                  if (state.flashcardEvent == FlashcardOneShotEvent.loading) {
                    return Center(
                      child: CupertinoActivityIndicator(radius: 16.r),
                    );
                  }
                  return ItemFlashcardStep(
                    flashcardSets: cubit.listFlashcardSets,
                    onRefresh: _refreshFlashcardSets,
                  );
                },
              ),
              // Add some padding at the bottom for better scrolling
              SizedBox(height: 24.h),
            ],
          ),
        ));
  }
}

void handleCreateFlashcards(
  BuildContext context,
  MyNoteDetailCubit cubit,
  NoteModel noteModel,
  bool isCommunityNote,
) {
  // Check both state and note's isGeneratingFlashCard from Hive
  final note = HiveService().noteBox.get(noteModel.id);
  if (cubit.state.isGeneratingFlashCard ||
      (note?.isGeneratingFlashCard ?? false)) {
    return;
  }

  if (cubit.appCubit.isUserProOrProlite()) {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.flashcard_sets_scr_create_clicked,
    );
    showCreateQuizFlashcardBottomSheet(
      context: context,
      isCreateQuiz: false,
      title: S.current.settings,
      onSubmit: (cardCount, difficulty, topic) {
        cubit.onFlashCard(
          customCardCount: cardCount,
          customDifficulty: difficulty,
          customTopic: topic,
        );
        Navigator.of(context).pop();
      },
    );
  } else {
    Navigator.of(context)
        .push(
      CupertinoPageRoute(
        builder: (context) => const PurchasePage(
          from: PurchasePageFrom.iapCreateNote,
        ),
      ),
    )
        .then((didPurchaseSuccess) {
      if (didPurchaseSuccess == true) {
        cubit.onFlashCard(communityNote: isCommunityNote);
      }
    });
  }
}
