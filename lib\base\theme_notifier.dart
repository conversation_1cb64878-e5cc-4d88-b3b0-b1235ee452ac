import 'package:flutter/material.dart';
import 'package:note_x/lib.dart';

class ThemeNotifier extends ValueNotifier<AppThemeEntity> {
  ThemeNotifier(AppThemeEntity initialTheme) : super(initialTheme) {
    WidgetsBinding.instance.platformDispatcher.onPlatformBrightnessChanged =
        () {
      if (value.id == 0) {
        notifyListeners();
      }
    };
  }

  bool get isDarkMode => value.brightness == Brightness.dark;

  void setTheme(AppThemeEntity newTheme) {
    if (value.id != newTheme.id) {
      value = newTheme;
      g<LocalService>().saveThemeId(newTheme.id);
      notifyListeners();
    }
  }

  ThemeMode getEffectiveTheme() {
    if (value.id == 0) {
      final isDark =
          WidgetsBinding.instance.platformDispatcher.platformBrightness ==
              Brightness.dark;
      return isDark ? ThemeMode.dark : ThemeMode.light;
    }
    return value.brightness == Brightness.dark
        ? ThemeMode.dark
        : ThemeMode.light;
  }
}

extension ThemeNotifierExtension on ThemeNotifier {
  void switchToSystemTheme() {
    setTheme(AppThemeConst.system);
  }

  void switchToDarkTheme() {
    setTheme(AppThemeConst.dark);
  }

  void switchToLightTheme() {
    setTheme(AppThemeConst.light);
  }

  String get themeName {
    switch (value.id) {
      case 0:
        return S.current.system;
      case 1:
        return S.current.light;
      case 2:
        return S.current.dark;
      default:
        return S.current.system;
    }
  }

  String get themeIcon {
    switch (value.id) {
      case 0:
        return Assets.icons.icSystemSetting;
      case 1:
        return Assets.icons.icLightSetting;
      case 2:
        return Assets.icons.icDarkSetting;
      default:
        return Assets.icons.icSystemSetting;
    }
  }
}
