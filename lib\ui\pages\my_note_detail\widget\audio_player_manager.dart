import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';

/// Singleton manager to handle audio player state independently of widget lifecycle
class AudioPlayerManager {
  static AudioPlayerManager? _instance;
  static AudioPlayerManager get instance => _instance ??= AudioPlayerManager._();

  AudioPlayerManager._();

  AudioPlayer? _audioPlayer;
  String? _currentAudioPath;
  String? _currentAudioUrl;

  // State notifiers
  final ValueNotifier<Duration> _audioDuration = ValueNotifier(Duration.zero);
  final ValueNotifier<Duration> _audioPosition = ValueNotifier(Duration.zero);
  final ValueNotifier<double> _playbackSpeed = ValueNotifier(1.0);
  final ValueNotifier<bool> _isDragging = ValueNotifier(false);

  // Position preservation
  Duration? _preservedPosition;
  bool _isRestoringPosition = false;

  StreamSubscription<Duration>? _positionSubscription;

  // Getters
  AudioPlayer? get audioPlayer => _audioPlayer;
  ValueNotifier<Duration> get audioDuration => _audioDuration;
  ValueNotifier<Duration> get audioPosition => _audioPosition;
  ValueNotifier<double> get playbackSpeed => _playbackSpeed;
  ValueNotifier<bool> get isDragging => _isDragging;

  bool get isInitialized => _audioPlayer != null;
  Duration get currentPosition => _audioPlayer?.position ?? Duration.zero;

  /// Initialize audio player with given audio source
  Future<void> initialize({
    required String audioFilePath,
    String? audioUrl,
  }) async {
    // Check if already initialized with same source
    if (_audioPlayer != null &&
        _currentAudioPath == audioFilePath &&
        _currentAudioUrl == audioUrl) {
      debugPrint('AudioPlayerManager: Already initialized with same source');
      return;
    }

    // Dispose existing player if different source
    if (_audioPlayer != null) {
      await dispose();
    }

    _currentAudioPath = audioFilePath;
    _currentAudioUrl = audioUrl;

    _audioPlayer = AudioPlayer();

    try {
      await (audioUrl != null && audioUrl.isNotEmpty
          ? _audioPlayer!.setUrl(audioUrl)
          : _audioPlayer!.setFilePath(audioFilePath));

      final duration = _audioPlayer!.duration;
      _audioDuration.value = duration ?? Duration.zero;

      // Setup position stream
      _positionSubscription = _audioPlayer!.positionStream.listen((position) {
        if (!_isDragging.value && !_isRestoringPosition) {
          _audioPosition.value = position;
        }
      });

      debugPrint('AudioPlayerManager: Initialized successfully');
    } catch (e) {
      debugPrint('AudioPlayerManager: Error initializing: $e');
      rethrow;
    }
  }

  /// Seek to specific position
  Future<void> seekToPosition(Duration position) async {
    if (_audioPlayer != null) {
      await _audioPlayer!.seek(position);
      _audioPosition.value = position;
    }
  }

  /// Play audio
  Future<void> play() async {
    await _audioPlayer?.play();
  }

  /// Pause audio
  Future<void> pause() async {
    await _audioPlayer?.pause();
  }

  /// Set playback speed
  Future<void> setSpeed(double speed) async {
    await _audioPlayer?.setSpeed(speed);
    _playbackSpeed.value = speed;
  }

  /// Preserve current position and pause audio
  void preservePosition() {
    if (_audioPlayer != null) {
      // Always pause audio when preserving position (for editing)
      if (_audioPlayer!.playing) {
        pause();
        debugPrint('AudioPlayerManager: Paused audio for editing');
      }

      // Only update preserved position if we don't have one yet
      if (_preservedPosition == null) {
        _preservedPosition = _audioPlayer!.position;
        debugPrint('AudioPlayerManager: Preserved position ${_preservedPosition?.inSeconds}s');
      } else {
        debugPrint('AudioPlayerManager: Audio paused, keeping existing preserved position at ${_preservedPosition?.inSeconds}s');
      }
    }
  }

  /// Restore preserved position
  Future<void> restorePosition() async {
    if (_preservedPosition != null && !_isRestoringPosition && _audioPlayer != null) {
      _isRestoringPosition = true;
      final positionToRestore = _preservedPosition!;
      debugPrint('AudioPlayerManager: Restoring position ${positionToRestore.inSeconds}s');

      try {
        await seekToPosition(positionToRestore);
        debugPrint('AudioPlayerManager: Successfully restored position');
      } catch (e) {
        debugPrint('AudioPlayerManager: Error restoring position: $e');
        rethrow;
      } finally {
        _preservedPosition = null;
        _isRestoringPosition = false;
      }
    }
  }

  /// Clear preserved position
  void clearPreservedPosition() {
    _preservedPosition = null;
    debugPrint('AudioPlayerManager: Cleared preserved position');
  }

  /// Force pause audio (for editing scenarios)
  Future<void> forcePause() async {
    if (_audioPlayer != null && _audioPlayer!.playing) {
      await pause();
      debugPrint('AudioPlayerManager: Force paused audio');
    }
  }

  /// Set dragging state
  void setDragging(bool isDragging) {
    _isDragging.value = isDragging;
  }

  /// Dispose audio player and clean up resources
  Future<void> dispose() async {
    await _positionSubscription?.cancel();
    _positionSubscription = null;

    await _audioPlayer?.dispose();
    _audioPlayer = null;

    _currentAudioPath = null;
    _currentAudioUrl = null;
    _preservedPosition = null;
    _isRestoringPosition = false;

    _audioDuration.value = Duration.zero;
    _audioPosition.value = Duration.zero;
    _playbackSpeed.value = 1.0;
    _isDragging.value = false;

    debugPrint('AudioPlayerManager: Disposed');
  }

  /// Dispose all resources (call when app is closing)
  Future<void> disposeAll() async {
    await dispose();

    _audioDuration.dispose();
    _audioPosition.dispose();
    _playbackSpeed.dispose();
    _isDragging.dispose();

    _instance = null;
    debugPrint('AudioPlayerManager: Disposed all resources');
  }
}
