import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

import '../cubit/onboarding_state.dart';

class CustomTabPage extends StatelessWidget {
  const CustomTabPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppConstants.kSpacingItem40,
                    Center(
                      child: CommonText(
                        S.current.customize_your_note_view,
                        style: TextStyle(
                          fontSize: context.isTablet ? 26 : 24.sp,
                          fontWeight: FontWeight.w600,
                          color: context.colorScheme.mainPrimary,
                        ),
                      ),
                    ),
                    AppConstants.kSpacingItem4,
                    Center(
                      child: CommonText(
                        S.current.select_your_note,
                        style: TextStyle(
                          fontSize: context.isTablet ? 18 : 16.sp,
                          fontWeight: FontWeight.w400,
                          color: context.colorScheme.mainGray,
                        ),
                      ),
                    ),
                    Center(
                      child: CommonText(
                        S.current.you_can_update_setting,
                        style: TextStyle(
                          fontSize: context.isTablet ? 18 : 16.sp,
                          fontWeight: FontWeight.w400,
                          color: context.colorScheme.mainGray,
                        ),
                      ),
                    ),
                    AppConstants.kSpacingItem34,
                    Center(
                      child: SvgPicture.asset(
                        Assets.icons.logoCustomTab,
                        width: 333.w,
                        height: 244.h,
                      ),
                    ),
                    AppConstants.kSpacingItem34,
                    const Center(child: SelectionPage()),
                    AppConstants.kSpacingItem32,
                  ],
                ),
              ),
            ),
            _buildContinueButton(context),
            AppConstants.kSpacingItem10,
          ],
        ),
      ),
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    return BlocBuilder<OnboardingCubit, OnboardingState>(
      builder: (context, state) {
        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: context.isTablet ? 180 : 48.w,
          ),
          child: AppCommonButton(
            width: context.isTablet
                ? MediaQuery.of(context).size.height * 0.4
                : double.infinity,
            height: 56,
            borderRadius: BorderRadius.circular(100.r),
            gradient: const LinearGradient(
              begin: Alignment.centerRight,
              end: Alignment.centerLeft,
              colors: AppColors.gradientCTABlue,
            ),
            textWidget: CommonText(
              S.current.continue_button,
              style: TextStyle(
                height: 1,
                fontSize: context.isTablet ? 18 : 16.sp,
                fontWeight: FontWeight.w600,
                color: context.colorScheme.mainPrimary,
              ),
            ),
            onPressed: () {
              context.read<OnboardingCubit>().finishOnboarding();
            },
          ),
        );
      },
    );
  }
}

class SelectionPage extends StatefulWidget {
  const SelectionPage({super.key});

  @override
  SelectionPageState createState() => SelectionPageState();
}

class SelectionPageState extends State<SelectionPage> {
  final _localService = GetIt.instance.get<LocalService>();

  final Map<String, String> svgIcons = {
    S.current.summary: Assets.icons.icHomeDoc,
    S.current.transcript: Assets.icons.icObTrans,
    S.current.slide_show: Assets.icons.icSlide01,
    S.current.mind_map: Assets.icons.icMindmapLayoutB,
    S.current.shorts: Assets.icons.icCustomShort,
    S.current.document_tab: Assets.icons.icCustomDoc,
    S.current.flashcard: Assets.icons.icCustomFlashcard,
    S.current.quizzes: Assets.icons.icCustomQuiz,
    S.current.podcast: Assets.icons.icCustomPodcast,
  };

  final List<String> selectedItems = [S.current.summary];

  void toggleSelection(String item) {
    if (item == S.current.summary) return;

    setState(() {
      if (selectedItems.contains(item)) {
        selectedItems.remove(item);
      } else {
        selectedItems.add(item);
      }
    });

    _localService.saveSelectedItems(selectedItems);
  }

  @override
  void initState() {
    super.initState();
    if (selectedItems.length == 1 &&
        selectedItems.contains(S.current.summary)) {
      _localService.saveSelectedItems(AppConstants.allTabs);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      alignment: WrapAlignment.center,
      children: AppConstants.allTabs.map((item) {
        final isSelected = selectedItems.contains(item);
        final isSummary = item == S.current.summary;

        final hasBorder = isSelected || isSummary;
        final backgroundColor = isSelected || isSummary
            ? context.colorScheme.mainBlue.withOpacity(0.1)
            : context.colorScheme.mainNeutral;

        final foregroundColor =
            isSelected || isSummary ? Colors.blueAccent : Colors.white;

        return GestureDetector(
          onTap: () => toggleSelection(item),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(30),
              border: hasBorder
                  ? Border.all(color: context.colorScheme.mainBlue, width: 1.5)
                  : null,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  svgIcons[item] ?? Assets.icons.icEmptySummary,
                  width: context.isTablet ? 20 : 20.w,
                  height: context.isTablet ? 20 : 20.h,
                  colorFilter: ColorFilter.mode(
                    foregroundColor,
                    BlendMode.srcIn,
                  ),
                ),
                AppConstants.kSpacingItemW8,
                CommonText(
                  item,
                  style: TextStyle(
                    color: foregroundColor,
                    fontSize: context.isTablet ? 16 : 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}
