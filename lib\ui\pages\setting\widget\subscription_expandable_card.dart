import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:note_x/lib.dart';

class SubscriptionExpandableCard extends StatefulWidget {
  final String title;
  final String? price;
  final String? originalPrice;
  final String? subtitle;
  final List<String>? features;
  final bool isSelected;
  final VoidCallback onTap;
  final String? saveText;
  final Color? cardColor;
  final Gradient? gradient;
  final String planType;
  final bool isCurrentPlan;
  final bool isTrial;

  const SubscriptionExpandableCard({
    Key? key,
    required this.title,
    this.price,
    this.originalPrice,
    this.subtitle,
    this.features,
    required this.isSelected,
    required this.onTap,
    this.cardColor,
    this.gradient,
    this.saveText,
    required this.planType,
    this.isCurrentPlan = false,
    this.isTrial = false,
  }) : super(key: key);

  @override
  State<SubscriptionExpandableCard> createState() =>
      _SubscriptionExpandableCardState();
}

class _SubscriptionExpandableCardState
    extends State<SubscriptionExpandableCard> {
  BoxDecoration _getDecoration() {
    return BoxDecoration(
      gradient: widget.isCurrentPlan
          ? null
          : (widget.isSelected ? widget.gradient : null),
      color: widget.isCurrentPlan
          ? context.colorScheme.mainNeutral
          : (widget.isSelected
              ? widget.cardColor?.withOpacity(0.1)
              : context.colorScheme.mainNeutral),
      borderRadius: BorderRadius.circular(16.r),
      border: widget.isCurrentPlan
          ? null
          : Border.all(
              color: widget.isSelected
                  ? (widget.cardColor ?? Colors.transparent)
                  : Colors.transparent,
              width: 2,
            ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 400),
        curve: Curves.fastOutSlowIn,
        margin: EdgeInsets.symmetric(vertical: 8.h),
        decoration: _getDecoration(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          if (widget.gradient != null)
                            GradientText(
                              text: widget.title,
                              gradient: widget.gradient!,
                              style: TextStyle(
                                  fontSize: context.isTablet ? 18 : 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: context.colorScheme.mainPrimary),
                            )
                          else
                            Text(
                              widget.title,
                              style: TextStyle(
                                fontSize: context.isTablet ? 18 : 16.sp,
                                fontWeight: FontWeight.w600,
                                color: widget.cardColor,
                              ),
                            ),
                          if (widget.isCurrentPlan) ...[
                            SizedBox(width: 8.w),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                color: widget.gradient != null
                                    ? null
                                    : widget.planType == 'free'
                                        ? AppColors.primaryBlue
                                        : (context.isDarkMode
                                            ? widget.cardColor
                                            : context.colorScheme.mainPrimary),
                                gradient: widget.gradient,
                                borderRadius: BorderRadius.circular(16.r),
                              ),
                              child: Text(
                                S.current.your_plan,
                                style: TextStyle(
                                  color: widget.planType == 'yearly'
                                      ? Colors.black
                                      : Colors.white,
                                  fontSize: context.isTablet ? 12 : 10.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                          if (!widget.isCurrentPlan &&
                              widget.saveText != null) ...[
                            SizedBox(width: 8.w),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 4.h,
                              ),
                              decoration: BoxDecoration(
                                color: widget.gradient != null
                                    ? null
                                    : widget.cardColor,
                                gradient: widget.gradient,
                                borderRadius: BorderRadius.circular(16.r),
                              ),
                              child: Text(
                                widget.saveText!,
                                style: TextStyle(
                                  color: context.colorScheme.mainNeutral,
                                  fontSize: context.isTablet ? 12 : 10.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      SvgPicture.asset(
                        widget.isCurrentPlan
                            ? Assets.icons.icDefaultAvatar
                            : widget.isSelected
                                ? widget.planType == 'yearly'
                                    ? Assets.icons.icAccountCheckYellow
                                    : widget.planType == 'monthly'
                                        ? Assets.icons.icAccountCheckViolet
                                        : Assets.icons.icAccountCheckBlue
                                : Assets.icons.icUncheckOb,
                        width: 24.w,
                        height: 24.h,
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  if (widget.price != null && widget.price!.isNotEmpty) ...[
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          widget.price ?? '',
                          style: TextStyle(
                            fontSize: context.isTablet ? 26 : 24.sp,
                            fontWeight: FontWeight.w600,
                            color: context.colorScheme.mainPrimary,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          widget.originalPrice ?? '',
                          style: TextStyle(
                            fontSize: context.isTablet ? 16 : 14.sp,
                            fontWeight: FontWeight.w400,
                            color:
                                context.colorScheme.mainGray.withOpacity(0.8),
                            decoration: TextDecoration.lineThrough,
                            decorationColor: context.colorScheme.mainGray,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                  ],
                  if (widget.subtitle != null && widget.subtitle!.isNotEmpty)
                    Text(
                      widget.subtitle ?? '',
                      style: TextStyle(
                        fontSize: context.isTablet ? 16 : 14.sp,
                        fontWeight: FontWeight.w400,
                        color: context.colorScheme.mainGray,
                      ),
                    ),
                  if (widget.features != null &&
                      widget.features!.isNotEmpty) ...[
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      child: Divider(height: 1.h),
                    ),
                    SizedBox(
                        height:
                            (widget.price != null && widget.price!.isNotEmpty ||
                                    widget.subtitle != null &&
                                        widget.subtitle!.isNotEmpty)
                                ? 16.h
                                : 8.h),
                    ...widget.features!.map((feature) {
                      return Padding(
                        padding: EdgeInsets.only(
                            bottom: widget.features!.last == feature ? 0 : 8.h),
                        child: Row(
                          children: [
                            Text(
                              '•',
                              style: TextStyle(
                                fontSize: context.isTablet ? 16 : 14.sp,
                                fontWeight: FontWeight.w400,
                                color: context.colorScheme.mainGray,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: Text(
                                feature,
                                style: TextStyle(
                                  fontSize: context.isTablet ? 16 : 14.sp,
                                  fontWeight: FontWeight.w400,
                                  color: context.colorScheme.mainGray,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
