import 'dart:io';
import 'package:el_tooltip/el_tooltip.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/ui/pages/upload_audio/cubit/upload_audio_state.dart';
import 'package:get_it/get_it.dart';

class UploadAudioPage extends StatefulWidget {
  static const routeName = 'UploadAudioPage';
  final SharedMediaFile? sharedMediaFile;
  final VoidCallback? onClose;
  final VoidCallback? onCreateNoteSuccess;
  final bool isFromWelcome;

  const UploadAudioPage({
    super.key,
    this.sharedMediaFile,
    this.isFromWelcome = false,
    this.onClose,
    this.onCreateNoteSuccess,
  });

  @override
  State<UploadAudioPage> createState() => _UploadAudioPageState();
}

class _UploadAudioPageState
    extends BasePageStateDelegate<UploadAudioPage, UploadAudioCubit> {
  @override
  void initState() {
    super.initState();
    if (widget.sharedMediaFile != null) {
      _handleShareMediaAudioFile(widget.sharedMediaFile!);
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          _onSelectAudioFile();
        }
      });
    }

    cubit.logEventTrackingOpenAudioPage();
  }

  @override
  void didUpdateWidget(UploadAudioPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.sharedMediaFile != null) {
      _handleShareMediaAudioFile(widget.sharedMediaFile!);
    }
  }

  @override
  void dispose() {
    super.dispose();
    widget.onClose?.call();
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<UploadAudioCubit, UploadAudioState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent &&
          current.oneShotEvent != CreateNoteOneShotEventUploadAudio.none,
      listener: (context, state) async {
        switch (state.oneShotEvent) {
          case CreateNoteOneShotEventUploadAudio.none:
            break;
          case CreateNoteOneShotEventUploadAudio.createNoteSuccessfully:
            // Save note model before reset state
            final noteModel = cubit.getAudioNote();

            // Reset state before navigating
            cubit.resetState();

            // Call the onClose and onCreateNoteSuccess callbacks
            widget.onClose?.call();
            widget.onCreateNoteSuccess?.call();

            Navigator.of(context).pop();
            final savedTabs =
                await GetIt.instance.get<LocalService>().loadSelectedItems();
            await Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => MyNoteDetailPage(
                  noteModel: noteModel,
                  isTablet: cubit.appCubit.isTablet,
                  from: NoteDetailPageFrom.createAudioNoteScreen,
                  savedTabs: savedTabs,
                ),
              ),
            );
            break;
          case CreateNoteOneShotEventUploadAudio.invalidFileType:
            showBlackCupertinoDialog(
              context: context,
              title: '',
              message: S.current.invalid_file_type,
              confirmButton: S.current.ok,
              onConfirm: () {},
              confirmButtonTextColor: context.colorScheme.mainBlue,
            );
            break;
          case CreateNoteOneShotEventUploadAudio.onShowIAPFromUploadAudioFile:
            Navigator.of(context).pushNamed(
              PurchasePage.routeName,
              arguments: {
                EventKey.from: PurchasePageFrom.iapCreateNote,
              },
            ).then((didPurchaseSuccess) => {
                  if (didPurchaseSuccess == true) {cubit.onSubmitUploadAudio()}
                });
            break;
          case CreateNoteOneShotEventUploadAudio
                .onShowLifetimeIAPFromUploadAudioFile:
            Navigator.of(context)
                .push(
                  MaterialPageRoute(
                    builder: (context) => const PurchaseLifeTimePageOldVer(
                      from: PurchaseLifetimeFrom.createNotePage,
                    ),
                    fullscreenDialog: true,
                  ),
                )
                .then((didPurchaseSuccess) => {
                      if (didPurchaseSuccess == true)
                        {cubit.onSubmitUploadAudio()}
                    });
            break;
        }
        cubit.resetEnumState();
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBarWidget(
          isShowLeftButton: true,
          title: S.current.audio,
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildUploadAudioFileScreen(),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  top: 16.h,
                  bottom: MediaQuery.of(context).viewInsets.bottom > 0
                      ? 16.h
                      : context.isTablet
                          ? 32
                          : 32.h),
              child: BlocBuilder<UploadAudioCubit, UploadAudioState>(
                buildWhen: (previous, current) =>
                    previous.audioFilePath != current.audioFilePath,
                builder: (context, state) {
                  return _buildButtonSubmit(
                    isEnable: state.audioFilePath.isNotEmpty,
                    createNoteType: NoteType.uploadAudio,
                  );
                },
              ),
            ),
          ],
        ));
  }

  Widget _buildUploadAudioFileScreen() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: context.isLandscape
                ? 75
                : cubit.appCubit.isTablet
                    ? 56
                    : 56.h,
            padding: cubit.appCubit.isTablet
                ? const EdgeInsets.symmetric(horizontal: 11, vertical: 8)
                : EdgeInsets.symmetric(horizontal: 11.w, vertical: 8.h),
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: AppColors.gradientCTABlue,
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
              ),
              borderRadius: cubit.appCubit.isTablet
                  ? BorderRadius.circular(54)
                  : BorderRadius.circular(54.r),
            ),
            child: BlocBuilder<UploadAudioCubit, UploadAudioState>(
              buildWhen: (previous, current) =>
                  previous.audioFilePath != current.audioFilePath,
              builder: (context, state) => state.audioFilePath.isEmpty
                  ? InkWell(
                      onTap: () => _onSelectAudioFile(),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            Assets.icons.icUploadFile,
                            colorFilter: const ColorFilter.mode(
                              AppColors.white,
                              BlendMode.srcIn,
                            ),
                            width: cubit.appCubit.isTablet ? 24 : 24.w,
                            height: cubit.appCubit.isTablet ? 24 : 24.w,
                          ),
                          AppConstants.kSpacingItemW4,
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 4.w),
                            child: CommonText(
                              S.current.upload_file,
                              style: TextStyle(
                                height: 1.5,
                                fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                                color: AppColors.white,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: CommonText(
                            state.audioFileName,
                            style: TextStyle(
                              fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                              fontWeight: FontWeight.w500,
                            ),
                            textColor: context.colorScheme.themeWhite,
                            maxLines: 1,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        GestureDetector(
                            onTap: () => cubit.clearFile(),
                            child: SvgPicture.asset(
                              Assets.icons.icCloseWhite,
                              width: cubit.appCubit.isTablet ? 24 : 24.w,
                              height: cubit.appCubit.isTablet ? 24 : 24.h,
                            )),
                      ],
                    ),
            ),
          ),
          AppConstants.kSpacingItem8,
          Center(
            child: CommonText(
              S.current.support_audio,
              style: TextStyle(
                fontSize: cubit.appCubit.isTablet ? 14 : 12.sp,
                fontWeight: FontWeight.w400,
                color: context.colorScheme.mainGray.withOpacity(0.86),
              ),
            ),
          ),
          AppConstants.kSpacingItem16,
          CommonText(
            S.current.folder,
            textColor: context.colorScheme.mainGray,
            style: TextStyle(
              fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
          AppConstants.kSpacingItem8,
          SizedBox(
            width: double.infinity,
            child: FolderDropdownView(
              folders: [
                FolderModel(
                    id: 'all_notes',
                    folderName: S.current.all_note,
                    backendId: ''),
                ...HiveFolderService.getAllFolders()
              ],
              selectedFolder: cubit.tabAudioFileChangeFolderNotifier,
              onMenuStateChanged: (isShowing) {
                if (isShowing) {}
              },
              from: FolderDropdownFrom.audio,
            ),
          ),
          AppConstants.kSpacingItem16,
          Row(
            children: [
              CommonText(
                S.current.speech_language,
                textColor: context.colorScheme.mainGray,
                style: TextStyle(
                  fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
              AppConstants.kSpacingItemW4,
              ElTooltip(
                padding: EdgeInsets.all(8.w),
                content: Container(
                  constraints: BoxConstraints(
                    maxWidth: 250.w,
                    maxHeight: 78.h,
                  ),
                  child: const LanguageTipsWidget(
                    isRecording: false,
                    isYoutube: false,
                    isCheckDocAndText: false,
                  ),
                ),
                position: ElTooltipPosition.bottomCenter,
                color: context.colorScheme.mainBlue,
                radius: Radius.circular(16.r),
                showChildAboveOverlay: true,
                showModal: true,
                child: SvgPicture.asset(
                  Assets.icons.icCommonInfoTooltip,
                  height: cubit.appCubit.isTablet ? 18 : 18.w,
                  width: cubit.appCubit.isTablet ? 18 : 18.w,
                  colorFilter: ColorFilter.mode(
                    context.colorScheme.mainPrimary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ],
          ),
          AppConstants.kSpacingItem8,
          SizedBox(
            width: double.infinity,
            child: LanguageDropdownView(
              initialLanguageCode: cubit.targetAudioFileLanguage?.code,
              onChanged: (lang) => {cubit.setAudioFileLanguage(lang)},
              onMenuStateChanged: (isShowing) {
                if (isShowing) {}
              },
              useCase: LanguageDropdownUseCase.recordAndAudioFile,
              from: LanguageDropdownFrom.audioFile,
            ),
          ),
          AppConstants.kSpacingItem16,
          AdvancedWidget(
            onSummaryStyleChanged: cubit.updateSummaryStyle,
            onWritingStyleChanged: cubit.updateWritingStyle,
            onAdditionalInstructionsChanged: cubit.updateAdditionalInstructions,
            onAdvancedToggled: cubit.setAdvancedEnabled,
          ),
        ],
      ),
    );
  }

  Future<void> _onSelectAudioFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: Platform.isAndroid ? FileType.any : FileType.custom,
        allowedExtensions:
            Platform.isAndroid ? null : cubit.validAudioExtensions,
      );
      if (result != null) {
        File file = File(result.files.single.path ?? '');
        String name = result.files.single.name;
        cubit.onAudioFilePicked(file.path, name);
      } else {}
    } catch (e) {
      debugPrint('Error picking file: $e');
    }
  }

  Widget _buildButtonSubmit({
    bool isEnable = true,
    required NoteType createNoteType,
  }) {
    final gradientColors = isEnable
        ? context.colorScheme.mainBlue
        : context.colorScheme.mainNeutral;
    return Container(
      alignment: Alignment.center,
      margin: EdgeInsets.symmetric(
        horizontal: context.isLandscape
            ? 360
            : cubit.appCubit.isTablet
                ? 240
                : 24.w,
      ),
      child: AppCommonButton(
        width: cubit.appCubit.isTablet ? 200 : 160.w,
        height: cubit.appCubit.isTablet ? 44 : 44.h,
        borderRadius: BorderRadius.circular(24.r),
        backgroundColor: gradientColors,
        textWidget: CommonText(
          S.current.add_to_notes,
          style: TextStyle(
            height: 1,
            fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
            fontWeight:
                cubit.appCubit.isTablet ? FontWeight.w600 : FontWeight.w500,
            color: isEnable
                ? context.colorScheme.themeWhite
                : context.colorScheme.mainPrimary.withOpacity(0.38),
          ),
        ),
        onPressed: () async {
          if (isEnable) {
            if (cubit.targetAudioFileLanguage == null ||
                cubit.targetAudioFileLanguage ==
                    const Language('err', 'Not found!')) {
              showNewCupertinoDialog(
                context: context,
                title: S.current.create_select_a_language,
                message: S.current.please_select_a_language,
                image: Assets.icons.icSelectLanguage,
                confirmButton: S.current.ok,
                onConfirm: () {},
              );
            } else {
              cubit.onSubmitUploadAudio();
            }
          }
        },
      ),
    );
  }

  void _handleShareMediaAudioFile(SharedMediaFile sharedMediaFile) async {
    final fileName = sharedMediaFile.path.split('/').last;
    final filePath = sharedMediaFile.path;
    cubit.onAudioFilePicked(filePath, fileName);
  }
}
