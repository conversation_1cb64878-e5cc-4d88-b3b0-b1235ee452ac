// ignore_for_file: constant_identifier_names

class EventName {
  static const String onboarding_get_started_next =
      'onboarding_get_started_next';
  static const String onboarding_choose_type_next =
      'onboarding_choose_type_next';
  static const String onboarding_1_student = 'onboarding_1_student';
  static const String onboarding_2_student = 'onboarding_2_student';
  static const String onboarding_3_student = 'onboarding_3_student';
  static const String onboarding_4_student = 'onboarding_4_student';
  static const String onboarding_5_student = 'onboarding_5_student';

  static const String onboarding_1_business = 'onboarding_1_business';
  static const String onboarding_2_business = 'onboarding_2_business';
  static const String onboarding_3_business = 'onboarding_3_business';
  static const String onboarding_4_business = 'onboarding_4_business';

  // login
  static const String login_with_apple = 'login_with_apple';
  static const String login_with_apple_onb_success =
      'login_with_apple_onb_success';
  static const String login_with_google = 'login_with_google';
  static const String login_with_google_onb_success =
      'login_with_google_onb_success';
  static const String login_with_email = 'login_with_email';
  static const String send_email_link = 'send_email_link';
  static const String login_with_email_failed = 'login_with_email_failed';
  static const String invalid_email_link = 'invalid_email_link';

  //iap events
  static const String iap_show = 'iap_show';
  static const String iap_week_click = 'iap_week_click';
  static const String iap_month_click = 'iap_month_click';
  static const String iap_year_click = 'iap_year_click';
  static const String iap_continue = 'iap_continue';
  static const String iap_purchase_success = 'iap_purchase_success';
  static const String iap_close = 'iap_close';

  static const String iap_lifetime_show = 'iap_lifetime_show';
  static const String iap_lifetime_close = 'iap_lifetime_close';
  static const String iap_lifetime_access = 'iap_lifetime_access';

  // home page
  static const String home_show = 'home_show';
  static const String home_pro = 'home_pro';
  static const String home_setting = 'home_setting';
  static const String home_community_notes = 'home_community_notes';
  static const String home_search = 'home_search';
  static const String home_tabbar_note = 'home_tabbar_note';
  static const String home_tabbar_folder = 'home_tabbar_folder';
  static const String home_tabbar_shared = 'home_tabbar_shared';
  static const String home_filter = 'home_filter';
  static const String home_tabbar_note_close_filter =
      'home_tabbar_note_close_filter';
  static const String home_tabbar_note_filter_updated =
      'home_tabbar_note_filter_updated';
  static const String home_tabbar_note_sort_updated =
      'home_tabbar_note_sort_updated';
  static const String home_note_item_tap = 'home_note_item_tap';
  static const String home_refresh_note = 'home_refresh_note';
  static const String home_refresh_folder = 'home_refresh_folder';

  /// Select 5 main features in the app
  static const String home_link = 'home_link';
  static const String home_audio = 'home_audio';
  static const String home_record = 'home_record';
  static const String home_document = 'home_document';
  static const String home_text = 'home_text';
  static const String home_image = 'home_image';
  static const String home_camera = 'home_camera';
  static const String home_referral = 'home_referral';
  static const String home_other_clicked = 'home_other_clicked';
  static const String moreOptionOnHomeMoveToFolder =
      'HomeScreen_MoreOption_MoveToFolderClicked';
  static const String moreOptionOnHomeDeleteNoteShow =
      'HomeScreen_MoreOption_DeleteNoteShow';
  static const String moreOptionOnHomeDeleteNote =
      'HomeScreen_MoreOption_DeleteNoteConfirm';

  // folder
  static const String folder_create_folder = 'folder_create_folder';
  static const String folder_unsynced_note = 'folder_unsynced_note';
  static const String folder_detail = 'folder_detail';
  static const String folder_create_folder_confirm =
      'folder_create_folder_confirm';
  static const String folder_create_folder_cancel =
      'folder_create_folder_cancel';
  static const String folder_detail_edit_folder = 'folder_detail_edit_folder';
  static const String folder_detail_edit_folder_cancel =
      'folder_detail_edit_folder_cancel';
  static const String folder_detail_edit_folder_confirm =
      'folder_detail_edit_folder_confirm';
  static const String folder_detail_delete_folder =
      'folder_detail_delete_folder';
  static const String folder_detail_delete_folder_confirm =
      'folder_detail_delete_folder_confirm';
  static const String folder_detail_delete_folder_checkbox_checked =
      'folder_detail_delete_folder_checkbox_checked';
  static const String folder_detail_delete_folder_checkbox_unchecked =
      'folder_detail_delete_folder_checkbox_unchecked';
  static const String folder_detail_delete_folder_cancel =
      'folder_detail_delete_folder_cancel';
  static const String folder_detail_more_pressed = 'folder_detail_more_pressed';
  static const String folder_detail_back = 'folder_detail_back';

  // youtube_open
  static const String weblink_show = 'weblink_show';
  static const String weblink_youtube_open = 'weblink_youtube_open';
  static const String weblink_add_to_note = 'weblink_add_to_note';
  static const String weblink_upload_fail = 'weblink_upload_fail';
  static const String weblink_choose_a_language = 'weblink_choose_a_language';
  static const String weblink_folder_selected = 'weblink_folder_selected';

  // record_audio_start
  static const String record_audio_start = 'record_audio_start';
  static const String record_audio_stop = 'record_audio_stop';
  static const String record_audio_pause = 'record_audio_pause';
  static const String record_audio_restart = 'record_audio_restart';
  static const String record_audio_done = 'record_audio_done';
  static const String record_audio_back = 'record_audio_back';
  static const String record_audio_folder_selected =
      'record_audio_folder_selected';
  static const String record_choose_a_language = 'record_choose_a_language';

  // upload audio
  static const String audio_upload_submit = 'audio_upload_submit';
  static const String record_upload_submit = 'record_upload_submit';
  static const String audio_upload_success = 'audio_upload_success';
  static const String audio_upload_fail = 'audio_upload_fail';
  static const String record_audio_upload_fail = 'record_audio_upload_fail';
  static const String upload_audio_choose_a_language =
      'upload_audio_choose_a_language';
  static const String upload_audio_folder_selected =
      'upload_audio_folder_selected';

  // note summary
  static const String note_detail_back = 'note_summary_back';
  static const String note_detail_show = 'note_detail_show';
  static const String note_detail_menu = 'note_summary_menu';
  static const String quizzes_scr_create_clicked = 'QuizzesScr_Create_Clicked';
  static const String quizzes_btmsht_create_clicked =
      'QuizzesBts_Create_Clicked';
  static const String flashcard_scr_create_clicked =
      'FlashcardScr_Create_Clicked';
  static const String flashcard_btmsht_create_clicked =
      'FlashcardBts_Create_Clicked';
  static const String flashcard_btmsht_create_fail = 'FlashcardBts_Create_Fail';
  static const String flashcard_btmsht_create_success =
      'FlashcardBts_Create_Success';
  static const String mind_map_scr_create_click = 'MindMapScr_Create_Clicked';
  static const String mind_map_scr_create_success = 'MindMapScr_Create_Success';
  static const String mind_map_scr_create_fail = 'MindMapScr_Create_Fail';

  static const String note_detail_edit = 'note_summary_edit';
  static const String note_detail_add_folder = 'note_summary_add_folder';
  static const String note_detail_add_folder_success =
      'note_summary_add_folder_success';
  static const String note_detail_share_summary = 'note_summary_share_summary';
  static const String note_detail_share_transcript =
      'note_summary_share_transcript';
  static const String note_detail_export_summary =
      'note_summary_export_summary';
  static const String note_detail_export_transcript =
      'note_summary_export_transcript';
  static const String note_detail_export_quiz = 'note_summary_export_quiz';
  static const String note_detail_export_flashcard =
      'note_summary_export_flashcard';
  static const String note_detail_share_note_link =
      'note_summary_share_note_link';
  static const String note_detail_delete = 'note_summary_delete';
  static const String note_detail_delete_cancel = 'note_summary_delete_cancel';
  static const String note_detail_delete_success =
      'note_summary_delete_success';
  static const String note_detail_done_button_pressed =
      'note_detail_done_button_pressed';
  static const String note_detail_translate_note = 'note_detail_translate_note';
  static const String note_detail_translate_note_submit =
      'note_detail_translate_note_submit';
  static const String note_detail_export_docx = 'note_summary_export_docx';
  static const String note_detail_export_pdf = 'note_summary_export_pdf';
  static const String note_detail_open_ai_chat = 'note_detail_open_ai_chat';
  static const String note_detail_close_ai_chat = 'note_detail_close_ai_chat';

  static const String note_summary_rating = 'note_summary_rating';
  static const String note_summary_rating_submit = 'note_summary_rating_submit';
  static const String note_summary_rating_close = 'note_summary_rating_close';
  static const String note_summary_edit_font_size =
      'note_summary_edit_font_size';
  static const String note_summary_find_and_replace =
      'note_summary_find_and_replace';
  static const String note_summary_show_replace = 'note_summary_show_replace';

  // note flashcard
  static const String note_flashcard_back = 'FlashcardScr_Back_Scroll';
  static const String note_flashcard_next = 'FlashcardScr_Next_Scroll';
  static const String note_flashcard_flip = 'FlashcardScr_Flip_Clicked';
  static const String flashcard_scr_add_flashcard_set_click =
      'FlashcardScr_AddFlashcardSet_Clicked';
  static const String flashcard_sets_scr_create_clicked =
      'FlashcardSetsScr_Create_Clicked';
  static const String note_delete_flashcard_set =
      'FlashcardSetsScr_Delete_Success';
  static const String note_edit_flashcard_set =
      'FlashcardSetsScr_EditName_Success';
  static const String flashcard_scr_back_clicked = 'FlashcardScr_Back_Clicked';

  // static const String note_flashcard_menu = 'note_flashcard_menu';
  static const String note_flashcard_export_docx =
      'FlashcardSetsScr_ExportDOCX_Success';
  static const String note_flashcard_export_pdf =
      'FlashcardSetsScr_ExportPDF_Success';

  // note_quizzes
  static const String quiz_sets_scr_back_clicked = 'QuizSetsScr_Back_Clicked';
  static const String quizzes_scr_add_quiz_set_click =
      'QuizzesScr_AddQuizSet_Clicked';
  static const String quiz_sets_scr_add_quiz_set_click =
      'QuizSetsScr_Create_Clicked';
  static const String quiz_sets_scr_delete_success =
      'QuizSetsScr_Delete_Success';
  static const String quiz_sets_scr_edit_name_success =
      'QuizSetsScr_EditName_Success';

  // static const String note_quizzes_menu = 'note_quizzes_menu';
  static const String note_quizzes_next = 'QuizzesScr_Next_Scroll';
  static const String note_quizzes_choose_answer =
      'QuizzesScr_ChooseAnswer_Clicked';
  static const String note_quiz_export_docx = 'QuizSetsScr_ExportDOCX_Success';
  static const String note_quiz_export_pdf = 'QuizSetsScr_ExportPDF_Success';

  static const String quizzes_btmsht_create_fail = 'QuizzesBts_Create_Fail';
  static const String quizzes_btmsht_create_success =
      'QuizzesBts_Create_Success';
  static const String quizzes_scr_show_dialog = 'QuizzesDlg_Show_Success';

  // note_mindmap
  static const String note_mindmap_reload = 'MindMapScr_Reload_Clicked';
  static const String note_mindmap_export = 'MindMapScr_Export_Clicked';
  static const String note_mindmap_export_png = 'MindMapScr_ExportPNG_Success';
  static const String note_mindmap_export_pdf = 'MindMapScr_ExportPDF_Success';
  static const String note_mindmap_export_docx =
      'MindMapScr_ExportDOCX_Success';
  static const String note_mindmap_export_md = 'MindMapScr_ExportMD_Success';
  static const String note_mindmap_export_jpeg =
      'MindMapScr_ExportJPEG_Success';
  static const String note_mindmap_full_screen =
      'MindMapScr_FullScreen_Clicked';
  static const String note_mindmap_full_screen_exit =
      'MindMapScr_FullScreen_Exit_Clicked';

  // note_transcript
  static const String note_transcript_export_docx = 'note_mindmap_export_docx';
  static const String note_transcript_export_pdf = 'note_mindmap_export_pdf';
  static const String transcript_edit_start = 'TranscriptScr_Edit_Started';
  static const String transcript_edit_save = 'TranscriptScr_Edit_Saved';
  static const String transcript_content_click = 'TranscriptScr_Content_Clicked';

  // audio player
  static const String audio_player_play = 'AudioPlayer_Play_Clicked';
  static const String audio_player_pause = 'AudioPlayer_Pause_Clicked';
  static const String audio_player_seek = 'AudioPlayer_Seek_Changed';
  static const String audio_player_speed_change = 'AudioPlayer_Speed_Changed';
  static const String audio_player_export = 'AudioPlayer_Export_Clicked';
  static const String audio_player_share = 'AudioPlayer_Share_Clicked';
  static const String audio_player_export_success = 'AudioPlayer_Export_Success';
  static const String audio_player_share_success = 'AudioPlayer_Share_Success';
  static const String audio_player_export_fail = 'AudioPlayer_Export_Failed';
  static const String audio_player_share_fail = 'AudioPlayer_Share_Failed';

  static const String document_upload_fail = 'document_upload_fail';
  static const String document_upload_submit = 'document_upload_submit';
  static const String document_upload_success = 'document_upload_success';
  static const String document_folder_selected = 'document_folder_selected';
  static const String document_choose_a_language = 'document_choose_a_language';
  static const String text_upload_submit = 'text_upload_submit';
  static const String text_upload_fail = 'text_upload_fail';
  static const String text_folder_selected = 'text_folder_selected';
  static const String text_choose_a_language = 'text_choose_a_language';

  // image
  static const String uploadImageSubmitClicked = 'UploadImage_Submit_Clicked';
  static const String uploadImageUploadFailed = 'UploadImage_Upload_Failed';
  static const String uploadImageFolderSelected = 'UploadImage_Folder_Selected';
  static const String uploadImageLanguageSelected =
      'UploadImage_Language_Selected';
  static const String uploadImageBackClicked = 'UploadImage_Back_Clicked';
  static const String uploadImageAddImage = 'UploadImage_AddImage_Clicked';
  static const String uploadImageDeleteImage =
      'UploadImage_DeleteImage_Clicked';
  static const String uploadImagePreviewImage =
      'UploadImage_PreviewImage_Clicked';
  static const String uploadImageSwapImages = 'UploadImage_SwapImages_Clicked';

  // image preview gallery
  static const String imagePreviewBackClicked = 'ImagePreview_Back_Clicked';

  // camera
  static const String cameraSubmitClicked = 'Camera_Submit_Clicked';
  static const String cameraUploadFailed = 'Camera_Upload_Failed';
  static const String cameraPhotoTaken = 'Camera_Photo_Taken';
  static const String cameraBackClicked = 'Camera_Back_Clicked';
  static const String cameraFolderSelected = 'Camera_Folder_Selected';
  static const String cameraLanguageSelected = 'Camera_Language_Selected';
  static const String cameraDeleteImage = 'Camera_DeleteImage_Clicked';
  static const String cameraPreviewImage = 'Camera_PreviewImage_Clicked';
  static const String cameraSwapImages = 'Camera_SwapImages_Clicked';
  static const String cameraShowDialogDenied = 'Camera_DialogDenied_Show';
  static const String cameraPermissionDenied = 'Camera_Permission_Denied';

  // camera capture page
  static const String takePhotoZoom = 'TakePhoto_Zoom_Changed';
  static const String takePhotoDoneClicked = 'TakePhoto_BtnDone_Clicked';
  static const String takePhotoPreviewClicked = 'TakePhoto_Preview_Clicked';
  static const String takePhotoInitFailed = 'TakePhoto_Init_Failed';
  static const String takePhotoMaxImagesReached = 'TakePhoto_MaxImages_Reached';
  static const String takePhotoFlashToggled = 'TakePhoto_BtnFlash_Toggled';

  static const String translate_choose_a_language =
      'transcript_choose_a_language';

  //referral
  static const String setting_enter_referral_code_click =
      'setting_enter_referral_code_click';
  static const String redeem_dialog_redeem_button_clicked =
      'redeem_dialog_redeem_button_clicked';
  static const String redeem_dialog_cancel_button_clicked =
      'redeem_dialog_cancel_button_clicked';
  static const String redeem_dialog_redeem_success =
      'redeem_dialog_redeem_success';

  // setting referral
  static const String setting_referral = 'setting_referral';
  static const String setting_referral_now = 'setting_referral_now';
  static const String setting_referral_copy = 'setting_referral_copy';
  static const String setting_iap_banner_clicked = 'setting_iap_banner_clicked';
  static const String setting_manage_recordings_click =
      'Setting_ManageRecordings_Click';
  static const String setting_manage_recordings_more_button_click =
      'SettingManageRecordings_MoreButton_Click';
  static const String setting_manage_recordings_Regenerate_button_click =
      'SettingManageRecordings_RegenerateButton_Click';
  static const String setting_manage_recordings_Share_button_click =
      'SettingManageRecordings_ShareButton_Click';
  static const String setting_manage_recordings_Delete_button_click =
      'SettingManageRecordings_DeleteButton_Click';
  // dialog referral
  static const String dialog_referral_now = 'dialog_referral_now';
  static const String dialog_referral_copy = 'dialog_referral_copy';
  static const String dialog_referral_close = 'dialog_referral_close';

  /// Shorts
  static const String shorts_scr_create_clicked = 'ShortsScr_Create_Clicked';
  static const String shorts_btmsht_create_clicked = 'ShortsBts_Create_Clicked';
  static const String podcast_scr_create_clicked = 'PodcastScr_Create_Clicked';
  static const String podcast_btmsht_create_clicked =
      'PodcastBts_Create_Clicked';
  static const String podcast_detail_download_clicked =
      'PodcastDetailScr_Export_Clicked';
  static const String podcast_detail_share_clicked =
      'PodcastDetailScr_Share_Clicked';
  static const String podcast_detail_download_success =
      'PodcastDetailScr_Export_Success';
  static const String podcast_detail_share_success =
      'PodcastDetailScr_Share_Success';
  static const String podcast_btmsht_back = 'PodcastBts_Back_Clicked';

  static const String shorts_detail_audio_empty = 'shorts_detail_audio_empty';
  static const String shorts_detail_return_code_fail =
      'shorts_detail_return_code_fail';
  static const String shorts_detail_merge_video_exception =
      'shorts_detail_merge_video_exception';
  static const String shorts_detail_video_init_fail =
      'shorts_detail_video_init_fail';
  static const String shorts_detail_share_clicked =
      'ShortsDetailScr_Share_Clicked';
  static const String shorts_detail_share_success =
      'ShortsDetailScr_Share_Success';
  static const String shorts_detail_share_exception =
      'shorts_detail_share_exception';
  static const String shorts_detail_download_exception =
      'shorts_detail_download_exception';
  static const String shorts_detail_download_clicked =
      'ShortsDetailScr_Export_Clicked';
  static const String shorts_detail_export_success =
      'ShortsDetailScr_Export_Success';

  static const String shortsDurationIsEmpty = 'shorts_duration_is_empty';
  static const String audioIsEmpty = 'audio_is_empty';
  static const String backGroundVideoIsEmpty = 'background_video_is_empty';
  static const String shorts_back = 'ShortsBts_Back_Clicked';
  static const String shorts_detail_back = 'ShortsDetailScr_Back_Clicked';
  // setting
  static const String setting_back = 'setting_back';
  static const String account_back = 'account_back';
  static const String whats_new_back = 'whats_new_back';
  static const String referral_back = 'referral_back';
  static const String setting_copy_user_id = 'setting_copy_user_id';
  static const String setting_rate_app = 'setting_rate_app';
  static const String setting_discord = 'setting_discord';
  static const String setting_notifications = 'setting_notifications';
  static const String setting_access_web = 'setting_access_web';
  static const String setting_account = 'setting_account';

  // account
  static const String account_more_pressed = 'account_more_pressed';
  static const String account_log_out = 'account_log_out';
  static const String account_log_out_cancel = 'account_log_out_cancel';
  static const String account_log_out_confirm = 'account_log_out_confirm';
  static const String account_delete_account = 'account_delete_account';
  static const String account_delete_account_cancel =
      'account_delete_account_cancel';
  static const String account_delete_account_confirm =
      'account_delete_account_confirm';
  static const String account_week_click = 'account_week_click';
  static const String account_month_click = 'account_month_click';
  static const String account_year_click = 'account_year_click';
  static const String account_change_plan = 'account_change_plan';

  static const String unknown_folder_selected = 'unknown_folder_selected';
  static const String unknown_choose_a_language = 'unknown_choose_a_language';
  static const String advanced_on = 'advanced_on';

  // notification
  static const String noti_allow_permission = 'noti_allow_permission';
  static const String noti_switch_note_ready = 'noti_switch_note_ready';
  static const String noti_confirm_add = 'noti_confirm_add';
  static const String noti_confirm_edit = 'noti_confirm_edit';
  static const String noti_confirm_delete = 'noti_confirm_delete';
  static const String noti_changed_time = 'noti_changed_time';
  static const String noti_changed_day = 'noti_changed_day';
  static const String noti_discard_changes = 'noti_discard_changes';
  static const String noti_create_reminder = 'noti_create_reminder';
  static const String noti_dialog_cancel = 'noti_dialog_cancel';
  static const String noti_delete_reminder = 'noti_delete_reminder';
  static const String noti_edit_reminder = 'noti_edit_reminder';
  static const String noti_back = 'noti_back';

  static const String general_event_create_note = 'general_event_create_note';
  static const String general_event_create_note_fail =
      'general_event_create_note_fail';
  //special
  static const String special_dialog = 'special_dialog';
  static const String special_dialog_open_now = 'special_dialog_open_now';
  static const String special_dialog_close = 'special_dialog_close';
  static const String iap_sale_off = 'iap_sale_off';
  // ai chat
  static const String ai_chat_open_saved_chat = 'ai_chat_open_saved_chat';
  static const String ai_chat_close_saved_chat = 'ai_chat_close_saved_chat';
  static const String ai_chat_save_to_note = 'ai_chat_save_to_note';
  static const String ai_chat_copy_chat_user = 'ai_chat_copy_chat_user';
  static const String ai_chat_copy_chat_ai = 'ai_chat_copy_chat_ai';
  static const String ai_chat_share_chat = 'ai_chat_share_chat';
  static const String ai_chat_send_chat = 'ai_chat_send_chat';
  static const String ai_chat_retry_chat = 'ai_chat_retry_chat';
  static const String ai_chat_stop_chat = 'ai_chat_stop_chat';
  static const String ai_chat_choose_suggestion = 'ai_chat_choose_suggestion';
  static const String ai_chat_open_saved_chat_detail =
      'ai_chat_open_saved_chat_detail';
  static const String ai_chat_saved_chat_copy = 'ai_chat_saved_chat_copy';
  static const String ai_chat_saved_chat_delete = 'ai_chat_saved_chat_delete';
  static const String ai_chat_saved_chat_close = 'ai_chat_saved_chat_close';

  static const settingSwitchTheme = "SettingScr_DarkModeSwitch_Clicked";
  static const switchModeDialogRestartNow = "SwitchModeDialog_RestartNow";
  // slide show
  static const slide_show_create_slide_show_clicked =
      "SlideShowScr_Create_Clicked";
  static const slide_show_close_click = "SlideShowScr_Close_Clicked";
  static const slide_show_choose_range_slide_click =
      "SlideShowScr_Range_Clicked";
  static const slide_show_Slide_template_click =
      "SlideShowScr_SlideTemplate_Clicked";
  static const slide_show_create_advanced_clicked =
      "SlideShowScr_CreateAdv_Clicked";
  static const slide_show_create_advanced_success =
      "SlideShowScr_CreateAdv_Success";
  static const slide_show_create_advanced_fail = "SlideShowScr_CreateAdv_Fail";
  static const slide_show_vertical_slide_clicked =
      "SlideShowScr_VerticalSlide_Clicked";
  static const slide_show_horizontal_slide_clicked =
      "SlideShowScr_HorizontalSlide_Clicked";
  static const slide_show_show_grid_slide_clicked =
      "SlideShowScr_GridSlide_Clicked";
  static const slide_show_save_slide_show_clicked =
      "SlideShowScr_SaveSlideShow_Clicked";
  static const slide_show_retry_slide_show_clicked =
      "SlideShowScr_Retry_Clicked";
  static const slide_show_save_pdf_success = "SlideShowScr_SavePDF_Success";
  static const slide_show_save_ppt_success = "SlideShowScr_SavePPT_Success";
  static const document_save_pdf_success = "DocumentScr_SavePDF_Success";
}

class EventKey {
  static const String from = 'from';
  static const String userID = 'user_id';
  static const String packageName = 'package_name';
  static const String reasonPurchaseFail = 'reason_purchase_fail';
}
