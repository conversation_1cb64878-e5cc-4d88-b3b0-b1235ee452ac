import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/chat/cubit/chat_state.dart';

class ChatPage extends StatefulWidget {
  final NoteModel noteModel;
  final bool autoFocus;
  final bool isLimitMessages;
  const ChatPage({
    Key? key,
    required this.noteModel,
    this.autoFocus = true,
    this.isLimitMessages = false,
  }) : super(key: key);

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage>
    with SingleTickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  final ValueNotifier<bool> _showScrollButton = ValueNotifier<bool>(false);
  final ValueNotifier<ChatTemplateModel?> _currentTemplate =
      ValueNotifier<ChatTemplateModel?>(null);

  @override
  void initState() {
    super.initState();
    _setupScrollListener();
    _setupInitialFocus();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      _showScrollButton.value = _scrollController.position.pixels > 0;
    });
  }

  void _setupInitialFocus() {
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await Future.delayed(const Duration(milliseconds: 300));
        if (mounted) {
          _focusNode.requestFocus();
        }
      });
    }
  }

  @override
  void dispose() {
    _focusNode.unfocus();
    _focusNode.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    _showScrollButton.dispose();
    _currentTemplate.dispose();
    super.dispose();
  }

  void _sendMessage(String message) {
    _focusNode.unfocus();
    String messageToSend = message;

    if (_currentTemplate.value != null) {
      final templatePrefix = "${_currentTemplate.value!.name} ";
      messageToSend = message.substring(templatePrefix.length);
    }

    final formattedMessage = messageToSend.replaceAll('\n', '\n\n\u200B\n');
    if (formattedMessage.isNotEmpty) {
      context.read<ChatCubit>().sendMessage(
            message: formattedMessage,
            noteId: widget.noteModel.backendNoteId,
            isLimitMessage: widget.isLimitMessages,
          );
      _messageController.clear();
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeOut,
        );
      }
    }
  }

  void _navigateToPurchasePage(BuildContext context) {
    final userType = GetIt.I.get<AppCubit>().getAppUser().userType;
    if (userType == UserType.free) {
      Navigator.push(
        context,
        CupertinoPageRoute(
          builder: (_) => const PurchasePage(
            from: PurchasePageFrom.iapChat,
          ),
          fullscreenDialog: true,
        ),
      );
    }
  }

  void _clearTemplate() {
    _currentTemplate.value = null;
    _messageController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatCubit, ChatState>(
      builder: (context, state) {
        final messages = context
            .read<ChatCubit>()
            .getMessagesForNote(widget.noteModel.backendNoteId);
        final bool showQuickOptions = messages.isEmpty && !state.isSending;

        return Container(
          color: context.colorScheme.mainSecondary,
          child: GestureDetector(
            onTap: () => _focusNode.unfocus(),
            child: SafeArea(
              bottom: false,
              minimum: EdgeInsets.only(bottom: context.isTablet ? 0 : 12.w),
              child: Stack(
                children: [
                  if (messages.isEmpty)
                    Positioned(
                      top: -84,
                      left: 0,
                      right: 0,
                      child: Image.asset(Assets.images.imgBgChat.path),
                    ),
                  Column(
                    mainAxisAlignment: messages.isEmpty
                        ? MainAxisAlignment.spaceBetween
                        : MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Align(
                          alignment: Alignment.topCenter,
                          child: showQuickOptions
                              ? ChatQuickOptions(
                                  textController: _messageController,
                                  focusNode: _focusNode,
                                  onSendMessage: _sendMessage,
                                  questions: widget.noteModel.suggestions
                                      .map((e) => e.question)
                                      .toList(),
                                  isLimitMessages: widget.isLimitMessages,
                                  noteId: widget.noteModel.backendNoteId,
                                  onNavigateToPurchase: _navigateToPurchasePage,
                                )
                              : ListView.builder(
                                  physics: const BouncingScrollPhysics(),
                                  reverse: true,
                                  controller: _scrollController,
                                  itemCount: messages.length,
                                  itemBuilder: (context, index) {
                                    final message = messages[index];
                                    return Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        ChatMessage(
                                          query: message.query,
                                          text: message.query,
                                          isUser: true,
                                          isCompleted: state.isCompleted,
                                          isSending: state.isSending,
                                          focusNode: _focusNode,
                                          note: widget.noteModel,
                                        ),
                                        if (state.isSending ||
                                            (message.answer.isNotEmpty &&
                                                message.isCompleted) ||
                                            message.hasError)
                                          ChatMessage(
                                            query: message.query,
                                            text: message.answer,
                                            isUser: false,
                                            isCompleted: message.isCompleted,
                                            isSending: state.isSending,
                                            focusNode: _focusNode,
                                            note: widget.noteModel,
                                            hasError: message.hasError,
                                            onRetry: message.hasError
                                                ? () {
                                                    AnalyticsService
                                                        .logAnalyticsEventNoParam(
                                                      eventName: EventName
                                                          .ai_chat_retry_chat,
                                                    );
                                                    context
                                                        .read<ChatCubit>()
                                                        .sendMessage(
                                                          message:
                                                              message.query,
                                                          noteId: widget
                                                              .noteModel
                                                              .backendNoteId,
                                                          isRetry: true,
                                                          isLimitMessage: widget
                                                              .isLimitMessages,
                                                        );
                                                  }
                                                : null,
                                          ),
                                      ],
                                    );
                                  },
                                ),
                        ),
                      ),
                      ValueListenableBuilder<bool>(
                        valueListenable: _showScrollButton,
                        builder: (context, showScrollButton, child) {
                          return TextFiledChat(
                            templates: widget.noteModel.chatTemplateSuggestions,
                            messageController: _messageController,
                            focusNode: _focusNode,
                            currentTemplate: _currentTemplate,
                            showGuide: showQuickOptions,
                            noteId: widget.noteModel.backendNoteId,
                            isLimitMessages: widget.isLimitMessages,
                            clearTemplate: _clearTemplate,
                            onSendMessage: _sendMessage,
                            navigateToPurchasePage: _navigateToPurchasePage,
                            stopReceiving: () =>
                                context.read<ChatCubit>().stopReceiving(),
                            showScrollButton: showScrollButton,
                          );
                        },
                      ),
                      KeyboardVisibilityBuilder(
                          builder: (context, isKeyboardVisible) {
                        return isKeyboardVisible
                            ? const SizedBox.shrink()
                            : SizedBox(height: 12.w);
                      }),
                    ],
                  ),
                  ValueListenableBuilder<bool>(
                    valueListenable: _showScrollButton,
                    builder: (context, showButton, child) {
                      return showButton
                          ? GestureDetector(
                              onTap: () {
                                if (_scrollController.hasClients) {
                                  _scrollController.animateTo(
                                    0,
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.easeOut,
                                  );
                                }
                              },
                              child: Padding(
                                padding: EdgeInsets.only(bottom: 110.h),
                                child: Align(
                                  alignment: Alignment.bottomCenter,
                                  child: Container(
                                    padding: EdgeInsets.all(4.r),
                                    decoration: BoxDecoration(
                                      color: context.colorScheme.mainNeutral,
                                      borderRadius: BorderRadius.circular(40.r),
                                    ),
                                    child: Icon(
                                      Icons.arrow_downward_rounded,
                                      color: context.colorScheme.mainPrimary,
                                      size: 24,
                                    ),
                                  ),
                                ),
                              ),
                            )
                          : const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
