import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/lib.dart';

class TabQuizWidget extends StatelessWidget {
  const TabQuizWidget({
    super.key,
    required this.widget,
    required this.cubit,
  });

  final MyNoteDetailPage widget;
  final MyNoteDetailCubit cubit;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: cubit.observerBoxNote,
      builder: (context, box, child) {
        final currentNote = box.get(widget.noteModel.id) ?? widget.noteModel;
        final quizzes = currentNote.quiz
            .map((e) => QuizDataMapperExtended().mapForModelHiveToViewModel(e))
            .map((e) => e.copyWith(
                  isAnswered: e.isAnswered,
                  answerChosenIndex: e.answerChosenIndex,
                ))
            .toList();
        return quizzes.isNotEmpty
            ? QuizPage(
                quizzes: quizzes,
                audioFilePath: currentNote.audioFilePath,
                audioUrl: currentNote.audioUrl,
                isCommunityNote: widget.isCommunityNote,
                isGeneratingQuiz: currentNote.isGeneratingQuiz,
              )
            : EmptyPage(
                image: Assets.images.imgDetailQuiz,
                title: S.current.click_start_quiz,
                isLoading: currentNote.noteStatus == NoteStatus.loading,
                noteModel: currentNote,
                contentButton: currentNote.isGeneratingQuiz
                    ? CupertinoActivityIndicator(
                        radius: 12.r,
                        color: context.colorScheme.themeWhite,
                      )
                    : Text(
                        S.current.content_button_quiz,
                        style: TextStyle(
                          fontSize: context.isTablet ? 16 : 14.sp,
                          fontWeight: context.isTablet
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: currentNote.noteStatus == NoteStatus.error ||
                                  currentNote.noteStatus == NoteStatus.loading
                              ? context.colorScheme.mainPrimary
                                  .withOpacity(0.38)
                              : context.colorScheme.themeWhite,
                        ),
                      ),
                onTap: () {
                  if (!currentNote.isGeneratingQuiz) {
                    _handleGenerateQuiz(context);
                  }
                },
              );
      },
    );
  }

  void _handleGenerateQuiz(BuildContext context) {
    cubit.appCubit.isUserProOrProlite()
        ? {
            AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.quizzes_scr_create_clicked,
            ),
            showCreateQuizFlashcardBottomSheet(
              context: context,
              isCreateQuiz: true,
              title: S.current.settings,
              onSubmit: (cardCount, difficulty, topic) {
                cubit.onQuiz(
                  communityNote: widget.isCommunityNote,
                  customNumQuestions: cardCount,
                  customDifficulty: difficulty,
                  customTopic: topic,
                );
                Navigator.of(context).pop();
              },
            )
          }
        : Navigator.of(context)
            .push(
              CupertinoPageRoute(
                builder: (context) => const PurchasePage(
                  from: PurchasePageFrom.iapCreateNote,
                ),
              ),
            )
            .then((didPurchaseSuccess) => {
                  if (didPurchaseSuccess == true)
                    {cubit.onQuiz(communityNote: widget.isCommunityNote)}
                });
  }
}
