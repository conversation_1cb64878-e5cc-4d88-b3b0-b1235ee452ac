import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../base/base_state.dart';

part 'onboarding_state.freezed.dart';

@freezed
class OnboardingState extends BaseState with _$OnboardingState {
  const factory OnboardingState({
    @Default(0) int currentIndex,
    @Default(true) bool isGetStartedScreen,
    @Default(false) bool isChooseTypeScreen,
    @Default(false) bool isCustomTabScreen,
    @Default(-1) int selectedTypeIndex,
    @Default(OnboardingOneShotEvent.none) OnboardingOneShotEvent oneShotEvent,
  }) = _OnboardingState;
}

enum OnboardingOneShotEvent {
  none,
}
