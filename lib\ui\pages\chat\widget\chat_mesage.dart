import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import 'package:super_context_menu/super_context_menu.dart';

class ChatMessage extends StatelessWidget {
  final String text;
  final bool isUser;
  final bool isCompleted;
  final bool isSending;
  final FocusNode focusNode;
  final bool hasError;
  final VoidCallback? onRetry;
  final NoteModel note;
  final String query;
  const ChatMessage({
    Key? key,
    required this.text,
    required this.isUser,
    required this.isCompleted,
    required this.isSending,
    required this.focusNode,
    this.hasError = false,
    this.onRetry,
    required this.note,
    required this.query,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double contentPadding = context.isTablet ? 8.0 : 8.0.w;
    final double iconSize = context.isTablet ? 16.0 : 16.0.w;
    final double borderRadius = context.isTablet ? 12.0 : 12.0.r;
    final double fontSize = context.isTablet ? 16.0 : 15.0.sp;
    final double smallFontSize = context.isTablet ? 14.0 : 12.0.sp;

    return GestureDetector(
      onTap: () {
        focusNode.unfocus();
      },
      child: Container(
        margin: EdgeInsets.only(
          top: contentPadding,
          bottom: contentPadding,
          left: isUser ? (context.isTablet ? 88.0 : 88.0.w) : contentPadding,
          right: context.isTablet ? 12.0 : 12.0.w,
        ),
        decoration: BoxDecoration(
          color: context.colorScheme.mainSecondary,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMessageContent(context, iconSize, borderRadius, fontSize),
            if (!isUser && text.isNotEmpty && isCompleted)
              _buildActionButtons(context, smallFontSize, iconSize),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context, double iconSize,
      double borderRadius, double fontSize) {
    return Row(
      mainAxisAlignment:
          isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isUser && isSending && text.isEmpty && !isCompleted) ...[
          SizedBox(width: context.isTablet ? 8.0 : 8.0.w),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Lottie.asset(
                Assets.videos.avatarNova,
                width: context.isTablet ? 24.0 : 24.0.w,
                height: context.isTablet ? 24.0 : 24.0.w,
                animate: !isCompleted,
              ),
              AppConstants.kSpacingItemW8,
              AnimatedTextEffect(
                text: S.current.thinking,
                style: TextStyle(
                  color: context.colorScheme.mainGray,
                  fontSize: context.isTablet ? 14 : 14.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ],
        isUser
            ? Flexible(
                child: ContextMenuWidget(
                  menuProvider: (MenuRequest request) {
                    return Menu(
                      children: [
                        MenuAction(
                            title: S.current.copy,
                            image: MenuImage.system(
                              "square.on.square",
                            ),
                            callback: () {
                              AnalyticsService.logAnalyticsEventNoParam(
                                eventName: EventName.ai_chat_copy_chat_user,
                              );
                              HapticFeedback.mediumImpact();
                              Clipboard.setData(ClipboardData(
                                  text: MyUtils.markdownToPlainText(text)));
                              CommonDialogs.showToast(
                                  S.current.copied_to_clipboard);
                            }),
                      ],
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: context.isTablet ? 16.0 : 16.0.w,
                      vertical: context.isTablet ? 8.0 : 8.0.w,
                    ),
                    decoration: BoxDecoration(
                      color: context.colorScheme.mainNeutral,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(borderRadius),
                        bottomLeft: Radius.circular(borderRadius),
                        bottomRight: Radius.circular(borderRadius),
                      ),
                    ),
                    child: Material(
                      type: MaterialType.transparency,
                      child: CommonText(
                        text,
                        style: TextStyle(
                          fontSize: fontSize,
                          color: context.colorScheme.mainPrimary,
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ),
                ),
              )
            : Flexible(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: 1.sw,
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: context.isTablet ? 8.0 : 8.0.w,
                    vertical: context.isTablet ? 8.0 : 8.0.w,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(borderRadius),
                  ),
                  child: SelectionArea(
                    child: MarkdownBody(
                      data: text,
                      styleSheet:
                          MyUtils.getChatMarkdownStyleSheet(context, fontSize),
                    ),
                  ),
                ),
              ),
        if (hasError && onRetry != null)
          _buildRetryButton(context, fontSize, iconSize),
      ],
    );
  }

  Widget _buildRetryButton(
      BuildContext context, double fontSize, double iconSize) {
    return Padding(
      padding: EdgeInsets.only(left: context.isTablet ? 2.0 : 2.0.w),
      child: GestureDetector(
        onTap: onRetry,
        child: Container(
          padding: EdgeInsets.symmetric(
              horizontal: context.isTablet ? 12.0 : 12.0.w,
              vertical: context.isTablet ? 8.0 : 8.0.h),
          decoration: BoxDecoration(
            color: context.colorScheme.mainNeutral,
            borderRadius: BorderRadius.circular(context.isTablet ? 8.0 : 8.0.r),
            border: Border.all(
              color: context.colorScheme.mainNeutral,
              width: 1,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.refresh,
                size: iconSize,
                color: context.colorScheme.mainPrimary,
              ),
              AppConstants.kSpacingItemW4,
              Text(
                S.current.retry,
                style: TextStyle(
                  fontSize: fontSize,
                  color: context.colorScheme.mainPrimary,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(
      BuildContext context, double fontSize, double iconSize) {
    return Padding(
      padding: EdgeInsets.only(left: context.isTablet ? 8.0 : 8.0.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSaveButton(context, fontSize, iconSize),
          context.isTablet
              ? AppConstants.kSpacingItemW8
              : AppConstants.kSpacingItemW12,
          _buildIconButton(
            context,
            iconAsset: Assets.icons.icCopyChat,
            onTap: () {
              AnalyticsService.logAnalyticsEventNoParam(
                eventName: EventName.ai_chat_copy_chat_ai,
              );
              HapticFeedback.mediumImpact();
              Clipboard.setData(
                  ClipboardData(text: MyUtils.markdownToPlainText(text)));
              CommonDialogs.showToast(S.current.copied_to_clipboard);
            },
            size: iconSize,
          ),
          context.isTablet
              ? AppConstants.kSpacingItemW8
              : AppConstants.kSpacingItemW16,
          _buildIconButton(
            context,
            iconAsset: Assets.icons.icShareNote,
            onTap: () {
              HapticFeedback.mediumImpact();
              final RenderBox box = context.findRenderObject() as RenderBox;
              final Rect rect = box.localToGlobal(Offset.zero) & box.size;
              ShareService().shareText(
                text: MyUtils.markdownToPlainText(text),
                sharePositionOrigin: rect,
                eventName: EventName.ai_chat_share_chat,
              );
            },
            size: iconSize,
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton(
      BuildContext context, double fontSize, double iconSize) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () async {
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.ai_chat_save_to_note,
        );
        HapticFeedback.mediumImpact();
        await context.read<ChatCubit>().saveToNotes(
              note: note,
              query: query,
              answer: text,
            );
      },
      child: Container(
        height: context.isTablet ? 24.0 : 24.0.w,
        padding:
            EdgeInsets.symmetric(horizontal: context.isTablet ? 8.0 : 8.0.w),
        decoration: BoxDecoration(
          color: context.colorScheme.mainBlue.withOpacity(0.2),
          borderRadius:
              BorderRadius.circular(context.isTablet ? 100.0 : 100.0.r),
          border: Border.all(
            color: context.colorScheme.mainBlue,
            width: 1,
          ),
        ),
        alignment: Alignment.center,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              height: context.isTablet ? 16.0 : 16.0.w,
              width: context.isTablet ? 16.0 : 16.0.w,
              Assets.icons.icGim,
              colorFilter: ColorFilter.mode(
                context.colorScheme.mainBlue,
                BlendMode.srcIn,
              ),
            ),
            context.isTablet
                ? const SizedBox(width: 4)
                : AppConstants.kSpacingItemW4,
            Flexible(
              fit: FlexFit.loose,
              child: Container(
                constraints: BoxConstraints(
                    maxWidth: context.isTablet ? 120.0 : 120.0.w),
                child: CommonText(
                  S.current.save_chat,
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: fontSize,
                    color: context.colorScheme.mainBlue,
                    height: 1,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconButton(
    BuildContext context, {
    required String iconAsset,
    VoidCallback? onTap,
    required double size,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      minSize: 0,
      onPressed: onTap,
      child: SvgPicture.asset(
        iconAsset,
        colorFilter: ColorFilter.mode(
          context.colorScheme.mainGray,
          BlendMode.srcIn,
        ),
        width: size,
        height: size,
      ),
    );
  }
}
