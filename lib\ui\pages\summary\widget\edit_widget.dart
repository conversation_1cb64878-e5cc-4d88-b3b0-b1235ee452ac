import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:note_x/lib.dart';

class EditWidget extends StatelessWidget {
  const EditWidget({
    super.key,
    required this.focusNode,
    required this.quillController,
    required this.scrollController,
    required this.fontSize,
    this.showQuillTool = true,
    this.readOnly = false,
  });
  final FocusNode focusNode;
  final QuillController quillController;
  final ScrollController scrollController;
  final double fontSize;
  final bool showQuillTool;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        showQuillTool && !readOnly
            ? QuillToolbarWrapper(
                controller: quillController,
                focusNode: focusNode,
              )
            : const SizedBox.shrink(),
        !readOnly
            ? Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Builder(
                  builder: (context) {
                    final text = quillController.document.toPlainText();
                    final textDirection = RtlUtils.getTextDirectionForContent(text);

                    return Directionality(
                      textDirection: textDirection,
                      child: QuillEditor(
                        focusNode: focusNode,
                        controller: quillController,
                        scrollController: scrollController,
                        configurations: QuillEditorConfigurations(
                          embedBuilders: [
                            DividerEmbedBuilder(),
                          ],
                          unknownEmbedBuilder: UnknownEmbedBuilder(),
                          spaceShortcutEvents: const [],
                          characterShortcutEvents: const [],
                          contextMenuBuilder: (context, editorState) {
                            final List<ContextMenuButtonItem> buttonItems =
                                List.from(editorState.contextMenuButtonItems);
                            buttonItems.insert(
                              3,
                              ContextMenuButtonItem(
                                label: S.current.find_and_replace,
                                onPressed: () {
                                  AnalyticsService.logAnalyticsEventNoParam(
                                    eventName:
                                        EventName.note_summary_find_and_replace,
                                  );
                                  // Đóng context menu trước
                                  editorState.toggleToolbar(true);
                                  editorState.selectionOverlay;

                                  SearchToolbar.show(
                                    context,
                                    quillController,
                                    scrollController,
                                  );
                                },
                                type: ContextMenuButtonType.custom,
                              ),
                            );
                            return AdaptiveTextSelectionToolbar.buttonItems(
                              anchors: editorState.contextMenuAnchors,
                              buttonItems: buttonItems,
                            );
                          },
                          scrollable: false,
                          autoFocus: false,
                          expands: false,
                          showCursor: true,
                          padding: EdgeInsets.zero,
                          keyboardAppearance: Brightness.dark,
                          customStyles: getDefaultStyles(context, fontSize),
                          minHeight: null,
                        ),
                      ),
                    );
                  }
                ),
              )
            : Builder(
                builder: (context) {
                  final text = quillController.document.toPlainText();
                  final textDirection = RtlUtils.getTextDirectionForContent(text);

                  return Directionality(
                    textDirection: textDirection,
                    child: Text(
                      text,
                      style: TextStyle(
                        fontSize: fontSize,
                        color: context.colorScheme.mainPrimary,
                      ),
                      maxLines: null,
                      textAlign: textDirection == TextDirection.rtl ? TextAlign.right : TextAlign.left,
                    ),
                  );
                },
              ),
      ],
    );
  }

  DefaultStyles getDefaultStyles(BuildContext context, double fontSize) {
    return DefaultStyles(
      h1: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize + 8,
          color: context.colorScheme.mainSummary,
          fontWeight: FontWeight.bold,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(8, 0),
        const VerticalSpacing(0, 0),
        null,
      ),
      h2: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize + 6,
          color: context.colorScheme.mainSummary,
          fontWeight: FontWeight.bold,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(8, 0),
        const VerticalSpacing(0, 0),
        null,
      ),
      h3: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize + 4,
          color: context.colorScheme.mainSummary,
          fontWeight: FontWeight.bold,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(16, 0),
        const VerticalSpacing(0, 0),
        null,
      ),
      h4: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize + 2,
          color: context.colorScheme.mainSummary,
          fontWeight: FontWeight.bold,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(16, 0),
        const VerticalSpacing(0, 0),
        null,
      ),
      h5: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize + 1,
          color: context.colorScheme.mainSummary,
          fontWeight: FontWeight.bold,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(16, 0),
        const VerticalSpacing(0, 0),
        null,
      ),
      h6: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize,
          color: context.colorScheme.mainSummary,
          fontWeight: FontWeight.bold,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(16, 0),
        const VerticalSpacing(0, 0),
        null,
      ),
      paragraph: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize,
          color: context.colorScheme.mainPrimary,
          height: 1.3,
          wordSpacing: 0.5,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(8, 8),
        const VerticalSpacing(8, 8),
        null,
      ),
      bold: GoogleFonts.inter(
        fontWeight: FontWeight.bold,
        fontSize: fontSize,
        color: context.colorScheme.mainPrimary,
      ),
      small: GoogleFonts.inter(
        fontSize: fontSize - 2,
        color: context.colorScheme.mainPrimary,
      ),
      underline: GoogleFonts.inter(
        decoration: TextDecoration.underline,
        fontSize: fontSize,
        color: context.colorScheme.mainPrimary,
      ),
      strikeThrough: GoogleFonts.inter(
        decoration: TextDecoration.lineThrough,
        fontSize: fontSize,
        color: context.colorScheme.mainPrimary,
      ),
      inlineCode: InlineCodeStyle(
        style: GoogleFonts.inter(
          color: context.colorScheme.mainPrimary,
          fontSize: fontSize - 1,
        ),
        backgroundColor: context.colorScheme.mainBlue,
        radius: const Radius.circular(4),
      ),
      link: GoogleFonts.inter(
        color: context.colorScheme.mainPrimary,
        decoration: TextDecoration.underline,
        fontSize: fontSize,
      ),
      placeHolder: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize,
          color: context.colorScheme.mainGray,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(4, 4),
        const VerticalSpacing(4, 4),
        null,
      ),
      lists: DefaultListBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize,
          color: context.colorScheme.mainPrimary,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(4, 4),
        const VerticalSpacing(4, 4),
        null,
        null,
      ),
      quote: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize,
          color: context.colorScheme.mainPrimary,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(2, 2),
        const VerticalSpacing(2, 2),
        BoxDecoration(
          color: context.colorScheme.mainNeutral,
          border: const Border(
            left: BorderSide(
              color: Colors.white,
              width: 2,
            ),
          ),
        ),
      ),
      code: DefaultTextBlockStyle(
        GoogleFonts.inter(
          color: context.colorScheme.mainPrimary,
          fontSize: fontSize - 1,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(0, 0),
        const VerticalSpacing(0, 0),
        BoxDecoration(
          color: context.colorScheme.mainNeutral,
          border: const Border(
            left: BorderSide(
              color: Colors.white,
              width: 2,
            ),
          ),
        ),
      ),
      indent: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize,
          color: context.colorScheme.mainPrimary,
        ),
        const HorizontalSpacing(16, 0),
        const VerticalSpacing(8, 8),
        const VerticalSpacing(8, 8),
        null,
      ),
      align: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize,
          color: context.colorScheme.mainPrimary,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(8, 8),
        const VerticalSpacing(8, 8),
        null,
      ),
      leading: DefaultTextBlockStyle(
        GoogleFonts.inter(
          fontSize: fontSize - 1,
          color: context.colorScheme.mainPrimary,
        ),
        HorizontalSpacing.zero,
        const VerticalSpacing(8, 8),
        const VerticalSpacing(8, 8),
        null,
      ),
      sizeSmall: GoogleFonts.inter(
        fontSize: fontSize - 4,
        color: context.colorScheme.mainPrimary,
      ),
      sizeLarge: GoogleFonts.inter(
        fontSize: fontSize + 4,
        color: context.colorScheme.mainPrimary,
      ),
      sizeHuge: GoogleFonts.inter(
        fontSize: fontSize + 8,
        color: context.colorScheme.mainPrimary,
      ),
    );
  }
}

class DividerEmbedBuilder extends EmbedBuilder {
  @override
  String get key => 'divider';

  @override
  Widget build(
    BuildContext context,
    QuillController controller,
    Embed node,
    bool readOnly,
    bool inline,
    TextStyle textStyle,
  ) {
    return Divider(
      height: 20,
      thickness: 1,
      color: context.colorScheme.mainGray,
    );
  }
}

class UnknownEmbedBuilder extends EmbedBuilder {
  @override
  String get key => 'unknown';

  @override
  Widget build(
    BuildContext context,
    QuillController controller,
    Embed node,
    bool readOnly,
    bool inline,
    TextStyle textStyle,
  ) {
    return const SizedBox.shrink();
  }
}
