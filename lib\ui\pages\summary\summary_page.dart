import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/summary/cubit/summary_page_state.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';

class SummaryPage extends StatefulWidget {
  static const String routeName = 'SummaryPage';
  final NoteModel noteModel;
  final bool isCommunityNote;
  final TabController tabController;
  final YoutubePlayerController controllerVideo;
  final Widget player;
  final FocusNode focusNode;
  final QuillController quillController;
  final bool didFetchNoteDataFail;

  const SummaryPage(
      {super.key,
      required this.noteModel,
      required this.isCommunityNote,
      required this.tabController,
      required this.controllerVideo,
      required this.player,
      required this.focusNode,
      required this.quillController,
      required this.didFetchNoteDataFail});

  @override
  State<SummaryPage> createState() => _SummaryPageState();
}

class _SummaryPageState
    extends BasePageStateDelegate<SummaryPage, SummaryPageCubit> {
  final ScrollController mainScrollController = ScrollController();
  double fontSize = 16;

  @override
  void initState() {
    super.initState();

    cubit.initData(widget.noteModel);
    AnalyticsService.logEventScreenView(
      screenClass: EventScreenClass.noteDetailPage,
      screenName: EventScreenName.note_summary,
    );
  }

  @override
  void dispose() {
    mainScrollController.dispose();
    super.dispose();
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<SummaryPageCubit, SummaryPageState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent &&
          current.oneShotEvent != SummaryPageOneShotEvent.none,
      listener: (context, state) {
        switch (state.oneShotEvent) {
          case SummaryPageOneShotEvent.none:
            break;
          case SummaryPageOneShotEvent.creatingSummarySuccessfully:
            CommonDialogs.showToast(S.current.summary_successful,
                length: Toast.LENGTH_LONG);
            Navigator.pop(context);
            break;
          case SummaryPageOneShotEvent.creatingSummaryFail:
            CommonDialogs.showInfoDialog(context,
                title: S.current.generate_note_fail,
                content: S.current.try_again,
                mainButtonTitle: S.current.ok);
            break;
          case SummaryPageOneShotEvent.onShowIAP:
            Navigator.of(context).pushNamed(
              PurchasePage.routeName,
              arguments: {
                EventKey.from: PurchasePageFrom.iapRetrySummaryScreen,
              },
            ).then((didPurchaseSuccess) => {
                  if (didPurchaseSuccess == true)
                    {cubit.onRetry(isUpload: true)}
                });
            break;
          case SummaryPageOneShotEvent.onShowIAPLifetimePro:
            Navigator.of(context)
                .push(
                  MaterialPageRoute(
                    builder: (context) => const PurchaseLifeTimePageOldVer(
                      from: PurchaseLifetimeFrom.recordPage,
                    ),
                    fullscreenDialog: true,
                  ),
                )
                .then((didPurchaseSuccess) => {
                      if (didPurchaseSuccess == true)
                        {cubit.onRetry(isUpload: true)}
                    });
            break;
        }
        cubit.resetEnumState();
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colorScheme.mainBackground,
      body: Stack(
        children: [
          Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  controller: mainScrollController,
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          left: 16.w,
                          right: 16.w,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildTopContent(widget.noteModel, widget.player),
                            if (widget.noteModel.type == 'youtube_url') ...[
                              Center(
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: context.colorScheme.mainNeutral,
                                    borderRadius: BorderRadius.circular(10.r),
                                  ),
                                  margin: EdgeInsets.only(bottom: 16.h),
                                  height: 4.w,
                                  width: 32.w,
                                ),
                              ),
                              AppConstants.kSpacingItem2,
                            ],
                          ],
                        ),
                      ),
                      widget.isCommunityNote
                          ? Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              child: MarkdownBody(
                                data: widget.noteModel.summary,
                                styleSheet: getMarkdownStyleSheet(context),
                              ),
                            )
                          : _buildProgressPage(),
                      SizedBox(
                        height: MediaQuery.of(context).viewPadding.bottom +
                            (widget.noteModel.type == 'audio' ||
                                    widget.noteModel.type == 'audio_file'
                                ? 125.h
                                : 63.h),
                      ),
                    ],
                  ),
                ),
              ),
              KeyboardVisibilityBuilder(
                builder: (context, isKeyboardVisible) {
                  return Container(
                    height: !isKeyboardVisible ? 0 : kToolbarHeight,
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressPage() {
    return ValueListenableBuilder(
        valueListenable: HiveService().noteBox.listenable(),
        builder: (context, box, child) {
          final note = box.get(widget.noteModel.id) ??
              HiveService().noteFailedBox.get(widget.noteModel.id);
          if (note == null || widget.didFetchNoteDataFail) {
            return const SizedBox.shrink();
          }

          return note.generatingAINote.progressValue == 1 &&
                      note.generatingAINote.status == ProcessStatus.completed ||
                  note.noteStatus == NoteStatus.success
              ? _buildSummaryView(note, context)
              : ProgressPage(
                  note: note,
                  onRetry: () => cubit.onRetry(
                      isUpload: note.uploadingToServer.status ==
                          ProcessStatus.failed),
                );
        });
  }

  Widget _buildSummaryView(NoteModel note, BuildContext context) {
    cubit.initFeedbackLogic(note, widget.isCommunityNote);
    return Column(
      children: [
        EditWidget(
          focusNode: widget.focusNode,
          quillController: widget.quillController,
          scrollController: mainScrollController,
          fontSize: fontSize,
        ),
        BlocBuilder<SummaryPageCubit, SummaryPageState>(
          buildWhen: (previous, current) =>
              previous.isShowFeedback != current.isShowFeedback,
          builder: (context, state) => state.isShowFeedback &&
                  note.noteStatus == NoteStatus.success
              ? Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.0.w),
                  child: FeedbackWidget(
                    onClose: () {
                      cubit.onHideFeedbackView();
                    },
                    onFeedbackSubmitted: (isPositive) async {
                      await HiveService().noteBox.put(
                          note.id, note.copyWith(isFeedbackSubmitted: true));

                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        mainScrollController.animateTo(
                          mainScrollController.position.maxScrollExtent,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      });
                    },
                    onStateChanged: () {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        mainScrollController.animateTo(
                          mainScrollController.position.maxScrollExtent,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      });
                    },
                    isFeedbackSubmitted: note.isFeedbackSubmitted,
                    appCubit: cubit.appCubit,
                    noteId: note.backendNoteId,
                  ),
                )
              : const SizedBox.shrink(),
        ),
      ],
    );
  }

  Widget _buildTopContent(NoteModel noteModel, Widget player) {
    return ValueListenableBuilder(
        valueListenable: HiveService().noteBox.listenable(),
        builder: (context, box, child) {
          final updatedNote = box.get(noteModel.id) ?? noteModel;

          return updatedNote.type == 'youtube_url' &&
                  isValidYoutubeLink(updatedNote.youtubeUrl)
              ? Padding(
                  padding: EdgeInsets.only(bottom: 12.h),
                  child: YoutubeValueBuilder(
                    controller: widget.controllerVideo,
                    builder: (context, value) {
                      return Stack(
                        children: [
                          AspectRatio(
                            aspectRatio: 16 / 9,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10.r),
                              child: player,
                            ),
                          ),
                          if (value.playerState == PlayerState.unknown) ...[
                            AspectRatio(
                              aspectRatio: 16 / 9,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: context.colorScheme.mainNeutral,
                                  borderRadius: BorderRadius.circular(10.r),
                                ),
                                child: const Center(
                                  child: CupertinoActivityIndicator(),
                                ),
                              ),
                            ),
                          ],
                        ],
                      );
                    },
                  ),
                )
              : const SizedBox.shrink();
        });
  }
}

MarkdownStyleSheet getMarkdownStyleSheet(BuildContext context) {
  return MarkdownStyleSheet(
    horizontalRuleDecoration: BoxDecoration(
      color: context.colorScheme.mainNeutral,
      border: Border(
        top: BorderSide(
          width: 1.0,
          color: context.colorScheme.mainGray,
        ),
      ),
    ),
    blockSpacing: 1.3.h,
    a: TextStyle(
      fontSize: context.isTablet ? 17 : 15.sp,
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary,
      fontWeight: FontWeight.w400,
    ),
    listBullet: TextStyle(
      fontSize: context.isTablet ? 17 : 15.sp,
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary,
      fontWeight: FontWeight.w400,
    ),
    h1: TextStyle(
      fontSize: context.isTablet ? 28 : 24.sp,
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary,
      fontWeight: FontWeight.w700,
    ),
    h2: TextStyle(
      fontSize: context.isTablet ? 24 : 20.sp, // Changed from 24 to 22
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary,
      fontWeight: FontWeight.w700,
    ),
    h2Padding: context.isTablet
        ? const EdgeInsets.only(top: 12)
        : EdgeInsets.only(top: 16.h),
    h3: TextStyle(
      fontSize: context.isTablet ? 20 : 18.sp, // Changed from 22 to 20
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary,
      fontWeight: FontWeight.w600,
    ),
    h3Padding: context.isTablet
        ? const EdgeInsets.symmetric(vertical: 8)
        : EdgeInsets.symmetric(vertical: 8.h),
    h4: TextStyle(
      fontSize: context.isTablet ? 19 : 17.sp, // Changed from 24 to 22
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary.withOpacity(0.7),
      fontWeight: FontWeight.w700,
    ),
    h5: TextStyle(
      fontSize: context.isTablet ? 18 : 16.sp, // Changed from 22 to 20
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainBlue.withOpacity(0.6),
      fontWeight: FontWeight.w700,
    ),
    h6: TextStyle(
      fontSize: context.isTablet ? 17 : 15.sp, // Changed from 20 to 18
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary.withOpacity(0.5),
      fontWeight: FontWeight.w700,
    ),
    p: TextStyle(
      // height: 1.3,
      fontSize: context.isTablet ? 17 : 15.sp,
      // Changed from 21 to 19
      fontWeight: FontWeight.w400,
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary,
    ),
    strong: TextStyle(
      fontSize: context.isTablet ? 17 : 15.sp, // Changed from 21 to 19
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary,
      fontWeight: FontWeight.w500,
    ),
    em: TextStyle(
      fontSize: context.isTablet ? 17 : 15.sp, // Changed from 22 to 20
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary,
      fontWeight: FontWeight.w400,
    ),
    del: TextStyle(
      fontSize: context.isTablet ? 17 : 15.sp, // Changed from 22 to 20
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary,
      decoration: TextDecoration.lineThrough,
    ),
    blockquote: TextStyle(
      fontSize: context.isTablet ? 17 : 15.sp, // Changed from 22 to 20
      fontFamily: AppConstants.fontSFPro,
      color: context.colorScheme.mainPrimary,
      fontWeight: FontWeight.w400,
    ),
    blockquotePadding: EdgeInsets.only(left: 16.w, top: 8.h, bottom: 8.h),
    blockquoteDecoration: BoxDecoration(
      color: context.colorScheme.mainNeutral,
      border: Border(
        left: BorderSide(
          color: Colors.white,
          width: 2.w,
        ),
      ),
    ),
    code: TextStyle(
      backgroundColor: context.colorScheme.mainNeutral,
      fontSize: context.isTablet ? 16 : 14.sp,
      // Changed from 22 to 20
      fontFamily: 'monospace',
      color: context.colorScheme.mainPrimary,
      fontWeight: FontWeight.w400,
    ),
  );
}
