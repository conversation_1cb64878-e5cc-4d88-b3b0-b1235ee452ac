import 'package:flutter/material.dart';

import '../../../lib.dart';

enum OnboardingScreen {
  getStarted,
  chooseType,
  summaryScreen,
  sliderScreen,
  mindMapScreen,
  flashCardScreen,
  quizScreen,
  generateAudioVideoScreen,
}

class OnboardingModel {
  final String imageUrl;
  final String title;
  final String content;
  final String subtitle;
  final OnboardingScreen onboardingScreen;
  final String jsonUrl;
  final String videoUrl;
  final bool isShowJsonToBgr;

  OnboardingModel({
    required this.imageUrl,
    required this.title,
    required this.content,
    required this.subtitle,
    required this.onboardingScreen,
    required this.jsonUrl,
    required this.videoUrl,
    required this.isShowJsonToBgr,
  });
}

extension OnboardingItems on OnboardingModel {
  static List<OnboardingModel> getItems(BuildContext context) {
    return [
      OnboardingModel(
        imageUrl: '',
        title: S.current.video_audio,
        content: S.current.content_hour,
        subtitle: S.current.insights_instantly,
        onboardingScreen: OnboardingScreen.summaryScreen,
        jsonUrl: Assets.videos.onb2,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.auto_generate_slides,
        content: S.current.craft_visual_stories,
        subtitle: S.current.every_note_you_take,
        onboardingScreen: OnboardingScreen.sliderScreen,
        jsonUrl: Assets.videos.onb6StudentBusiness,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.interactive_ai_flashcards,
        content: S.current.boost_knowledge,
        subtitle: S.current.retention_quickly,
        onboardingScreen: OnboardingScreen.mindMapScreen,
        jsonUrl: Assets.videos.onb3Student,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.interactive_flashcards_quiz,
        content: S.current.learn_faster_through,
        subtitle: S.current.active_recall,
        onboardingScreen: OnboardingScreen.quizScreen,
        jsonUrl: Assets.videos.onb4Student,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.onboarding_generate_audio_video_title,
        content: S.current.onboarding_generate_audio_video_content,
        subtitle: S.current.onboarding_generate_audio_video_sub_content,
        onboardingScreen: OnboardingScreen.generateAudioVideoScreen,
        jsonUrl: '',
        videoUrl: Assets.videos.onb5Student,
        isShowJsonToBgr: false,
      ),
    ];
  }
}

extension OnboardingProfessionalItems on OnboardingModel {
  static List<OnboardingModel> getProfessionalItems(BuildContext context) {
    return [
      OnboardingModel(
        imageUrl: '',
        title: S.current.video_audio,
        content: context.isTablet
            ? S.current.transform_meetings
            : S.current.transform_meetings,
        subtitle: context.isTablet
            ? S.current.actionable_intelligence
            : S.current.actionable_intelligence,
        onboardingScreen: OnboardingScreen.summaryScreen,
        jsonUrl: Assets.videos.onb2,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.auto_generate_slides,
        content: S.current.craft_visual_stories,
        subtitle: S.current.every_note_you_take,
        onboardingScreen: OnboardingScreen.sliderScreen,
        jsonUrl: Assets.videos.onb6StudentBusiness,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.interactive_ai_flashcards,
        content: S.current.visualize_strategies,
        subtitle: S.current.uncover_opportunities,
        onboardingScreen: OnboardingScreen.quizScreen,
        jsonUrl: Assets.videos.businessOnb3,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.onboarding_generate_audio_video_title,
        content: S.current.onboarding_generate_audio_video_content,
        subtitle: S.current.onboarding_generate_audio_video_sub_content,
        onboardingScreen: OnboardingScreen.generateAudioVideoScreen,
        jsonUrl: '',
        videoUrl: Assets.videos.businessOnb4,
        isShowJsonToBgr: false,
      ),
    ];
  }
}

extension OnboardingLandScapeItems on OnboardingModel {
  static List<OnboardingModel> getDataStudentItem(BuildContext context) {
    return [
      OnboardingModel(
        imageUrl: '',
        title: S.current.video_audio,
        content: S.current.insight_instantly,
        subtitle: '',
        onboardingScreen: OnboardingScreen.summaryScreen,
        jsonUrl: Assets.videos.onb2,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.auto_generate_slides,
        content: S.current.craft_visual_from_every_note,
        subtitle: '',
        onboardingScreen: OnboardingScreen.sliderScreen,
        jsonUrl: Assets.videos.onb6StudentBusiness,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.interactive_ai_flashcards,
        content: S.current.between_concepts,
        subtitle: '',
        onboardingScreen: OnboardingScreen.mindMapScreen,
        jsonUrl: Assets.videos.onb3Student,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.interactive_flashcards_quiz,
        content: S.current.learn_faster_through_active_recall,
        subtitle: '',
        onboardingScreen: OnboardingScreen.quizScreen,
        jsonUrl: Assets.videos.onb4Student,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.onboarding_generate_audio_video_title,
        content: S.current.onboarding_generate_audio_video_full_content,
        subtitle: '',
        onboardingScreen: OnboardingScreen.generateAudioVideoScreen,
        jsonUrl: '',
        videoUrl: Assets.videos.onb5Student,
        isShowJsonToBgr: false,
      ),
    ];
  }
}

extension OnboardingLandScapeProfessionalItems on OnboardingModel {
  static List<OnboardingModel> getDataProfessionalItem(BuildContext context) {
    return [
      OnboardingModel(
        imageUrl: '',
        title: S.current.video_audio,
        content: S.current.transform_meetings_into_actionable_intelligence,
        subtitle: '',
        onboardingScreen: OnboardingScreen.summaryScreen,
        jsonUrl: Assets.videos.onb2,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.auto_generate_slides,
        content: S.current.craft_visual_from_every_note,
        subtitle: '',
        onboardingScreen: OnboardingScreen.sliderScreen,
        jsonUrl: Assets.videos.onb6StudentBusiness,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.interactive_ai_flashcards,
        content: S.current.visualize_strategies_opportunities,
        subtitle: '',
        onboardingScreen: OnboardingScreen.quizScreen,
        jsonUrl: Assets.videos.businessOnb3,
        videoUrl: '',
        isShowJsonToBgr: true,
      ),
      OnboardingModel(
        imageUrl: '',
        title: S.current.onboarding_generate_audio_video_title,
        content: S.current.onboarding_generate_audio_video_full_content,
        subtitle: '',
        onboardingScreen: OnboardingScreen.generateAudioVideoScreen,
        jsonUrl: '',
        videoUrl: Assets.videos.businessOnb4,
        isShowJsonToBgr: false,
      ),
    ];
  }
}
