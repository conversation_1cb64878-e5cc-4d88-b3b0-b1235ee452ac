import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';
import '../../../base/base_page_state.dart';
import '../my_note_detail/cubit/my_note_detail_state.dart';

class QuizSetsPage extends StatefulWidget {
  static const String routeName = 'QuizSetsPage';
  final NoteModel noteModel;
  final bool isCommunityNote;
  final MyNoteDetailCubit parentCubit;

  const QuizSetsPage(
      {super.key,
      required this.noteModel,
      required this.isCommunityNote,
      required this.parentCubit});

  @override
  QuizSetsPageState createState() => QuizSetsPageState();
}

class QuizSetsPageState
    extends BasePageStateDelegate<QuizSetsPage, MyNoteDetailCubit> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      cubit.getAllQuizSets(widget.noteModel.backendNoteId);
    });
  }

  @override
  MyNoteDetailCubit? get parentCubit => widget.parentCubit;

  void _refreshFlashcardSets() {
    cubit.getAllQuizSets(widget.noteModel.backendNoteId);
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<MyNoteDetailCubit, MyNoteDetailState>(
      listenWhen: (previous, current) =>
          previous.isGeneratingQuiz != current.isGeneratingQuiz ||
          previous.quizOneShotEvent != current.quizOneShotEvent,
      listener: (context, state) {
        if (state.isGeneratingQuiz) {
          Navigator.of(context).pop({'selectQuizTab': true});
          return;
        }

        if (state.chooseQuiz == ChooseQuizOneShotEvent.loading) {
          CommonDialogs.showLoadingDialog();
        } else if (state.chooseQuiz == ChooseQuizOneShotEvent.success ||
            state.chooseQuiz == ChooseQuizOneShotEvent.error) {
          CommonDialogs.closeLoading(); // Dismiss loading dialog
        }
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
        appBar: AppBarWidget(
          isShowLeftButton: true,
          title: S.current.quiz_set,
          isCloseButton: true,
          onPressed: () {
            AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.quiz_sets_scr_back_clicked,
            );
            Navigator.of(context).pop();
          },
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              AppConstants.kSpacingItem8,
              AppCommonButton(
                borderRadius: BorderRadius.circular(54.r),
                margin: EdgeInsets.symmetric(
                  horizontal: 16.w,
                ),
                gradient: const LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: AppColors.gradientCTABlue,
                  transform: GradientRotation(pi),
                ),
                leftIcon: SvgPicture.asset(Assets.icons.icAdd),
                textWidget: CommonText(
                  S.current.content_button_quiz,
                  style: TextStyle(
                    fontSize: context.isTablet ? 18 : 16.sp,
                    fontWeight: FontWeight.w500,
                    color: context.colorScheme.themeWhite,
                    height: 1,
                  ),
                ),
                onPressed: () {
                  handleCreateQuiz(
                    context,
                    cubit,
                    widget.noteModel,
                    widget.isCommunityNote,
                  );
                },
              ),
              AppConstants.kSpacingItem8,
              Center(
                child: CommonText(
                  S.current.max_3_flashcard_sets,
                  style: TextStyle(
                    fontSize: context.isTablet ? 14 : 12.sp,
                    fontWeight: FontWeight.w400,
                    color: context.colorScheme.mainGray,
                  ),
                ),
              ),
              AppConstants.kSpacingItem12,
              BlocBuilder<MyNoteDetailCubit, MyNoteDetailState>(
                buildWhen: (previous, current) =>
                    previous.quizOneShotEvent != current.quizOneShotEvent,
                builder: (context, state) {
                  if (state.quizOneShotEvent == QuizOneShotEvent.loading) {
                    return Center(
                      child: CupertinoActivityIndicator(radius: 16.r),
                    );
                  }
                  return ItemQuizStep(
                    quizSets: cubit.listQuizSets,
                    onRefresh: _refreshFlashcardSets,
                  );
                },
              ),
              // Add some padding at the bottom for better scrolling
              SizedBox(height: 24.h),
            ],
          ),
        ));
  }

  void handleCreateQuiz(
    BuildContext context,
    MyNoteDetailCubit cubit,
    NoteModel noteModel,
    bool isCommunityNote,
  ) {
    final note = HiveService().noteBox.get(noteModel.id);
    if (cubit.state.isGeneratingQuiz || (note?.isGeneratingQuiz ?? false)) {
      return;
    }

    if (cubit.appCubit.isUserProOrProlite()) {
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.quiz_sets_scr_add_quiz_set_click,
      );
      showCreateQuizFlashcardBottomSheet(
        context: context,
        isCreateQuiz: true,
        title: S.current.settings,
        onSubmit: (cardCount, difficulty, topic) {
          cubit.onQuiz(
            customNumQuestions: cardCount,
            customDifficulty: difficulty,
            customTopic: topic,
          );
          Navigator.of(context).pop();
        },
      );
    } else {
      Navigator.of(context)
          .push(
        CupertinoPageRoute(
          builder: (context) => const PurchasePage(
            from: PurchasePageFrom.iapCreateNote,
          ),
        ),
      )
          .then((didPurchaseSuccess) {
        if (didPurchaseSuccess == true) {
          cubit.onFlashCard(communityNote: isCommunityNote);
        }
      });
    }
  }
}
