import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/lib.dart';

class DropdownController {
  OverlayEntry? overlayEntry;
  final LayerLink layerLink = LayerLink();
  final GlobalKey containerKey = GlobalKey();
  final ValueNotifier<VoiceModel?> selectedVoiceNotifier =
      ValueNotifier<VoiceModel?>(null);

  void showDropdown(BuildContext context, List<VoiceModel> voices,
      Function(VoiceModel) onSelect) {
    if (overlayEntry != null) {
      hideDropdown();
      return;
    }
    final RenderBox renderBox =
        containerKey.currentContext!.findRenderObject() as RenderBox;
    final size = renderBox.size;

    overlayEntry = _buildOverlayEntry(size, voices, onSelect);
    Overlay.of(context).insert(overlayEntry!);
  }

  OverlayEntry _buildOverlayEntry(
      Size size, List<VoiceModel> voices, Function(VoiceModel) onSelect) {
    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: hideDropdown,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          Positioned(
            width: size.width,
            child: CompositedTransformFollower(
              link: layerLink,
              showWhenUnlinked: false,
              offset: Offset(0.0, size.height + 4.0),
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(16.r),
                child: Container(
                  constraints: const BoxConstraints(maxHeight: 300),
                  decoration: BoxDecoration(
                    color: context.colorScheme.mainNeutral,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemCount: voices.length,
                    itemBuilder: (context, index) {
                      return DropdownItem(
                        voice: voices[index],
                        isSelected: voices[index].voiceId ==
                            selectedVoiceNotifier.value?.voiceId,
                        onTap: () {
                          onSelect(voices[index]);
                        },
                        isFirst: index == 0,
                        isLast: index == voices.length - 1,
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void hideDropdown() {
    overlayEntry?.remove();
    overlayEntry = null;
  }

  void dispose() {
    hideDropdown();
    selectedVoiceNotifier.dispose();
  }
}
