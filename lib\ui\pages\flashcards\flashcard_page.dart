import 'package:flutter/material.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class FlashCardPage extends StatefulWidget {
  final List<FlashCardModel> flashcards;
  final String title;
  final String? audioFilePath;
  final String? audioUrl;
  final bool isCommunityNote;
  final bool isGeneratingFlashCard;

  const FlashCardPage({
    required this.flashcards,
    required this.title,
    super.key,
    this.audioFilePath,
    this.audioUrl,
    required this.isCommunityNote,
    this.isGeneratingFlashCard = false,
  });

  @override
  State<FlashCardPage> createState() => _FlashCardPageState();
}

class _FlashCardPageState extends State<FlashCardPage> {
  final CardSwiperController _swiperController = CardSwiperController();
  final ValueNotifier<int> _currentIndexNotifier = ValueNotifier<int>(0);
  final ValueNotifier<bool> _isQuestionSide = ValueNotifier<bool>(true);
  TextEditingController editController = TextEditingController();

  @override
  void dispose() {
    _swiperController.dispose();
    _currentIndexNotifier.dispose();
    _isQuestionSide.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(FlashCardPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.flashcards != oldWidget.flashcards) {
      _currentIndexNotifier.value = 0;
      _isQuestionSide.value = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: context.colorScheme.mainBackground,
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 16.w),
            child: Column(
              children: [
                context.isTablet
                    ? AppConstants.kSpacingItem0
                    : AppConstants.kSpacingItem16,
                Flexible(
                  flex: 6,
                  child: Container(
                    width: double.infinity,
                    margin: EdgeInsets.symmetric(
                      horizontal: context.isTablet ? 70.w : 16.w,
                    ),
                    child: widget.isGeneratingFlashCard
                        ? Center(
                            child: Stack(
                              children: [
                                Lottie.asset(
                                  Assets.videos.starGenerate,
                                  width: 200,
                                  height: 200,
                                ),
                                Positioned.fill(
                                  bottom: 0,
                                  child: Align(
                                    alignment: Alignment.bottomCenter,
                                    child: AnimatedTextEffect(
                                      text: S.current.gen_ai,
                                      style: TextStyle(
                                        color: AppColors.white,
                                        fontSize: context.isTablet ? 16 : 14.sp,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ValueListenableBuilder<int>(
                            valueListenable: _currentIndexNotifier,
                            builder: (context, currentIndex, _) {
                              return CardSwiper(
                                controller: _swiperController,
                                cardsCount: widget.flashcards.length,
                                numberOfCardsDisplayed: 1,
                                onSwipe: _onSwipe,
                                onUndo: _onUndo,
                                onEnd: _onEnd,
                                backCardOffset: const Offset(0, 0),
                                padding: const EdgeInsets.all(0),
                                allowedSwipeDirection:
                                    const AllowedSwipeDirection.symmetric(
                                  horizontal: true,
                                  vertical: false,
                                ),
                                cardBuilder: (context,
                                    index,
                                    horizontalThresholdPercentage,
                                    verticalThresholdPercentage) {
                                  return FlashCardQAContainer(
                                    key: ValueKey<int>(currentIndex),
                                    question: widget
                                        .flashcards[currentIndex].question,
                                    answer:
                                        widget.flashcards[currentIndex].answer,
                                    currentIndex: currentIndex,
                                    totalIndex: widget.flashcards.length,
                                    transcriptJson: widget
                                        .flashcards[currentIndex]
                                        .transcriptJson,
                                    audioFilePath: widget.audioFilePath,
                                    audioUrl: widget.audioUrl,
                                    isCommunityNote: widget.isCommunityNote,
                                    isQuestionSide: _isQuestionSide,
                                  );
                                },
                              );
                            },
                          ),
                  ),
                ),
                AppConstants.kSpacingItem200,
              ],
            ),
          ),

          /// Status Question
          Positioned(
            left: 0,
            right: 0,
            child: widget.isGeneratingFlashCard
                ? const SizedBox.shrink()
                : ValueListenableBuilder<bool>(
                    valueListenable: _isQuestionSide,
                    builder: (context, isQuestion, _) {
                      return Lottie.asset(
                        isQuestion
                            ? Assets.videos.detailQuestion
                            : Assets.videos.detailAnswerFlashcard,
                        width: 64,
                        height: 64,
                      );
                    },
                  ),
          ),

          /// .... Dot indicator
          Positioned(
            left: 0,
            right: 0,
            bottom: 176.h,
            child: Center(
              child: widget.isGeneratingFlashCard
                  ? const SizedBox.shrink()
                  : ValueListenableBuilder<int>(
                      valueListenable: _currentIndexNotifier,
                      builder: (context, currentIndex, _) {
                        return AnimatedSmoothIndicator(
                          activeIndex: currentIndex,
                          count: widget.flashcards.length,
                          effect: ScrollingDotsEffect(
                            activeDotColor: context.colorScheme.mainBlue,
                            dotColor: context.colorScheme.mainGray,
                            dotHeight: 6.h,
                            dotWidth: 6.w,
                          ),
                        );
                      },
                    ),
            ),
          ),

          /// Transcript
          Positioned(
            left: 0,
            right: 0,
            bottom: 100.h,
            child: ValueListenableBuilder<int>(
              valueListenable: _currentIndexNotifier,
              builder: (context, currentIndex, _) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: ValueListenableBuilder<bool>(
                    valueListenable: _isQuestionSide,
                    builder: (context, isQuestion, _) {
                      return !isQuestion &&
                              widget.flashcards[currentIndex].context.isNotEmpty
                          ? Center(
                              child: ShadowedTextButton(
                                onPressed: () =>
                                    showModalBottomSheetTranscriptContext(
                                  context,
                                  widget.audioFilePath ?? '',
                                  widget.audioUrl ?? '',
                                  widget.flashcards[currentIndex].context,
                                  widget.isCommunityNote,
                                ),
                              ),
                            )
                          : const SizedBox.shrink();
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _onBackFlashCard() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_flashcard_back,
    );
  }

  void _onNextFlashCard() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_flashcard_next,
    );
  }

  bool _onSwipe(
    int previousIndex,
    int? currentIndex,
    CardSwiperDirection direction,
  ) {
    if (direction == CardSwiperDirection.right) {
      if (_currentIndexNotifier.value > 0) {
        _currentIndexNotifier.value--;
        _isQuestionSide.value = true;
        _onBackFlashCard();
      }
    } else if (direction == CardSwiperDirection.left) {
      if (_currentIndexNotifier.value < widget.flashcards.length - 1) {
        _currentIndexNotifier.value++;
        _isQuestionSide.value = true;
        _onNextFlashCard();
      }
    }
    return true;
  }

  bool _onUndo(
    int? previousIndex,
    int currentIndex,
    CardSwiperDirection direction,
  ) {
    if (previousIndex != null) {
      _currentIndexNotifier.value = previousIndex;
      _isQuestionSide.value = true;
    }
    return true;
  }

  void _onEnd() {}
}
