import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import 'package:just_audio/just_audio.dart';
import 'package:note_x/lib.dart';
// ignore: depend_on_referenced_packages
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'audio_player_manager.dart';

// Interface to expose the audio player
abstract class AudioPlayerInterface {
  AudioPlayer get audioPlayer;
  Future<void> seekToPosition(Duration position);
  Duration get currentPosition;
  void preservePosition();
  Future<void> restorePosition();
  void clearPreservedPosition();
  Future<void> forcePause();
}

class AudioPlayerWidget extends StatefulWidget {
  final String audioFilePath;
  final String? audioUrl;
  final bool isCommunityNote;
  final bool fullScreen;
  final bool isShowMore;
  final Color backgroundColor;
  final GlobalKey<State<AudioPlayerWidget>>? audioPlayerKey;

  const AudioPlayerWidget({
    Key? key,
    required this.audioFilePath,
    this.audioUrl,
    required this.isCommunityNote,
    this.fullScreen = false,
    this.isShowMore = true,
    required this.backgroundColor,
    this.audioPlayerKey,
  }) : super(key: audioPlayerKey ?? key);

  @override
  State<AudioPlayerWidget> createState() => _AudioPlayerWidgetState();
}

class _AudioPlayerWidgetState extends State<AudioPlayerWidget>
    with AutomaticKeepAliveClientMixin
    implements AudioPlayerInterface {
  AudioPlayer? _audioPlayer;
  final ValueNotifier<Duration> _audioDuration = ValueNotifier(Duration.zero);
  final ValueNotifier<Duration> _audioPosition = ValueNotifier(Duration.zero);
  final ValueNotifier<double> _playbackSpeed = ValueNotifier(1.0);
  final List<double> _playbackSpeeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
  final AppCubit appCubit = GetIt.instance.get<AppCubit>();
  int _currentSpeedIndex = 2;
  bool _isInitialized = false;

  // Implement the interface methods
  @override
  AudioPlayer get audioPlayer => _audioPlayer ?? AudioPlayer();

  @override
  Future<void> seekToPosition(Duration position) async {
    await AudioPlayerManager.instance.seekToPosition(position);
  }

  @override
  Duration get currentPosition => AudioPlayerManager.instance.currentPosition;

  @override
  void preservePosition() {
    AudioPlayerManager.instance.preservePosition();
  }

  @override
  Future<void> restorePosition() async {
    await AudioPlayerManager.instance.restorePosition();
  }

  @override
  void clearPreservedPosition() {
    AudioPlayerManager.instance.clearPreservedPosition();
  }

  @override
  Future<void> forcePause() async {
    await AudioPlayerManager.instance.forcePause();
  }

  @override
  void initState() {
    super.initState();
    _initAudioPlayerManager();
  }

  Future<void> _initAudioPlayerManager() async {
    if (_isInitialized) return;

    try {
      await AudioPlayerManager.instance.initialize(
        audioFilePath: widget.audioFilePath,
        audioUrl: widget.audioUrl,
      );

      // Use manager's audio player
      _audioPlayer = AudioPlayerManager.instance.audioPlayer;

      // Listen to manager's notifiers
      AudioPlayerManager.instance.audioPosition.addListener(_onPositionChanged);
      AudioPlayerManager.instance.audioDuration.addListener(_onDurationChanged);
      AudioPlayerManager.instance.playbackSpeed.addListener(_onSpeedChanged);

      if (mounted) {
        setState(() {
          _audioDuration.value = AudioPlayerManager.instance.audioDuration.value;
          _isInitialized = true;
        });
      }
    } catch (e) {
      if (mounted && !e.toString().contains('-1009')) {
        CommonDialogs.showErrorDialog(
          context,
          title: S.current.unable_load_audio,
          content: S.current.server_error,
        );
      }
    }
  }

  void _onPositionChanged() {
    if (mounted) {
      _audioPosition.value = AudioPlayerManager.instance.audioPosition.value;
    }
  }

  void _onDurationChanged() {
    if (mounted) {
      _audioDuration.value = AudioPlayerManager.instance.audioDuration.value;
    }
  }

  void _onSpeedChanged() {
    if (mounted) {
      _playbackSpeed.value = AudioPlayerManager.instance.playbackSpeed.value;
    }
  }

  @override
  void didUpdateWidget(AudioPlayerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the audio source changed, reinitialize
    if (oldWidget.audioFilePath != widget.audioFilePath ||
        oldWidget.audioUrl != widget.audioUrl) {
      _initAudioPlayerManager();
    }
  }

  @override
  void dispose() {
    // Remove listeners
    AudioPlayerManager.instance.audioPosition.removeListener(_onPositionChanged);
    AudioPlayerManager.instance.audioDuration.removeListener(_onDurationChanged);
    AudioPlayerManager.instance.playbackSpeed.removeListener(_onSpeedChanged);

    // Dispose local notifiers
    _audioDuration.dispose();
    _audioPosition.dispose();
    _playbackSpeed.dispose();

    // Note: Don't dispose the manager here as it might be used by other widgets
    super.dispose();
  }



  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    if (!_isInitialized || _audioPlayer == null) {
      return SizedBox(
        height: 60.h,
        child: Center(
          child: CircularProgressIndicator(
            color: context.colorScheme.mainPrimary,
          ),
        ),
      );
    }

    return StreamBuilder<PlayerState>(
      stream: _audioPlayer!.playerStateStream,
      builder: (context, snapshot) {
        final playerState = snapshot.data;
        final isPlaying = (playerState?.playing ?? false) &&
            playerState?.processingState != ProcessingState.completed;

        return SizedBox(
          width: widget.fullScreen ? double.infinity : null,
          child: Stack(
            alignment: widget.fullScreen
                ? Alignment.center
                : (appCubit.isReverseView
                    ? Alignment.centerLeft
                    : Alignment.centerRight),
            children: [
              // Player controls container
              AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOutCubic,
                width: isPlaying || widget.fullScreen
                    ? null
                    : context.isTablet
                        ? 65
                        : 58,
                decoration: BoxDecoration(
                  color: widget.fullScreen || isPlaying
                      ? widget.backgroundColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(100.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildPlayPauseButton(),
                    if (isPlaying || widget.fullScreen)
                      Expanded(
                        child: ClipRect(
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(child: _buildProgressBar()),
                              _buildTimeDisplay(),
                              AppConstants.kSpacingItemW8,
                              _buildPlaybackSpeedButton(),
                              if (context.isTablet) AppConstants.kSpacingItemW4,
                              if (widget.isShowMore) _buildShareButton(),
                              AppConstants.kSpacingItemW8,
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShareButton() {
    return PullDownButton(
      itemBuilder: (pullDownContext) => [
        _buildMenuItem(
          S.of(context).export_audio,
          Assets.icons.icExport01,
          () => (widget.audioUrl != null && widget.audioUrl!.isNotEmpty)
              ? _downloadAudioFile(pullDownContext)
              : _exportAudioLocal(pullDownContext),
        ),
        _buildMenuItem(
          S.of(context).share_file,
          Assets.icons.icShareNote,
          () => (widget.audioUrl != null && widget.audioUrl!.isNotEmpty)
              ? _shareAudioLink(pullDownContext)
              : _exportAudioLocal(pullDownContext),
        ),
      ],
      routeTheme: PullDownMenuRouteTheme(
        backgroundColor: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(16.r),
        width: context.isTablet ? 200 : 200.w,
      ),
      menuOffset: context.isTablet ? 6 : 6.w,
      buttonBuilder: (contextUi, showMenu) => IconButton(
        padding: EdgeInsets.all(context.isTablet ? 8 : 4.w),
        icon: SvgPicture.asset(
          Assets.icons.icMore,
          width: context.isTablet ? 32 : 20.w,
          height: context.isTablet ? 32 : 20.h,
          colorFilter: ColorFilter.mode(
            context.colorScheme.mainGray,
            BlendMode.srcIn,
          ),
        ),
        onPressed: showMenu,
      ),
    );
  }

  PullDownMenuItem _buildMenuItem(
      String title, String iconAsset, Function() onTap) {
    return PullDownMenuItem(
      itemTheme: PullDownMenuItemTheme(
        textStyle: TextStyle(fontSize: context.isTablet ? 16 : 14.sp),
      ),
      title: title,
      iconWidget: SvgPicture.asset(
        iconAsset,
        colorFilter:
            ColorFilter.mode(context.colorScheme.mainPrimary, BlendMode.srcIn),
      ),
      onTap: onTap,
    );
  }

  Future<void> _exportAudioLocal(BuildContext context) async {
    try {
      debugPrint('exportAudioLocal: ${widget.audioFilePath}');
      final file = File(widget.audioFilePath);
      HapticFeedback.lightImpact();
      if (await file.exists()) {
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.audio_player_export,
        );
        // ignore: use_build_context_synchronously
        final box = context.findRenderObject() as RenderBox?;
        await ShareService().shareFile(
          filePath: file.path,
          sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
        );
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.audio_player_export_success,
        );
      } else {
        _showErrorSnackBar(S.current.not_found_audio);
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.audio_player_export_fail,
        );
      }
    } catch (e) {
      _showErrorSnackBar(S.current.unable_share_audio);
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.audio_player_export_fail,
      );
    }
  }

  Future<void> _shareAudioLink(BuildContext context) async {
    debugPrint('shareAudioLink: ${widget.audioUrl}');
    if (widget.audioUrl != null) {
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.audio_player_share,
      );
      final box = context.findRenderObject() as RenderBox?;
      await ShareService().shareLink(
        link: widget.audioUrl!,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
      );
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.audio_player_share_success,
      );
    }
  }

  Future<void> _downloadAudioFile(BuildContext context) async {
    final BuildContext currentContext = context;
    final RenderBox? box = currentContext.findRenderObject() as RenderBox?;
    final Rect? sharePosition =
        box != null ? box.localToGlobal(Offset.zero) & box.size : null;
    try {
      if (widget.audioUrl == null || widget.audioUrl!.isEmpty) {
        _showErrorSnackBar(S.current.unable_download_file);
        return;
      }
      final appDir = await getTemporaryDirectory();
      final fileName = path.basename(widget.audioUrl!);
      final filePath = path.join(appDir.path, fileName);
      final response = await http.get(Uri.parse(widget.audioUrl!));
      if (response.statusCode != 200) {
        _showErrorSnackBar(S.current.unable_download_file);
        return;
      }
      final file = File(filePath);
      await file.writeAsBytes(response.bodyBytes);
      // Ensure the file exists and is readable
      if (await file.exists()) {
        if (mounted && sharePosition != null) {
          await ShareService().shareFile(
            filePath: file.path,
            sharePositionOrigin: sharePosition,
          );
        }
      } else {
        _showErrorSnackBar(S.current.unable_download_file);
      }
    } catch (e) {
      debugPrint('Error downloading audio file: $e');
      if (mounted) {
        _showErrorSnackBar(S.current.unable_download_file);
      }
    }
  }

  void _showSnackBar(String message, Color backgroundColor) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: backgroundColor),
    );
  }

  void _showErrorSnackBar(String message) => _showSnackBar(message, Colors.red);

  Widget _buildPlayPauseButton() {
    return StreamBuilder<PlayerState>(
      stream: _audioPlayer?.playerStateStream,
      builder: (context, snapshot) {
        final playerState = snapshot.data;
        final playing = playerState?.playing;
        final processingState = playerState?.processingState;

        String iconAsset;
        VoidCallback onPressed;

        if (playing != true) {
          iconAsset = appCubit.isReverseView
              ? Assets.icons.icFlipPlayWhite
              : Assets.icons.icPlayWhite;
          onPressed = () {
            AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.audio_player_play,
            );
            AudioPlayerManager.instance.play();
          };
        } else if (processingState != ProcessingState.completed) {
          iconAsset = Assets.icons.icPause;
          onPressed = () {
            AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.audio_player_pause,
            );
            AudioPlayerManager.instance.pause();
          };
        } else {
          iconAsset = appCubit.isReverseView
              ? Assets.icons.icFlipPlayWhite
              : Assets.icons.icPlayWhite;
          onPressed = () {
            AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.audio_player_play,
            );
            AudioPlayerManager.instance.seekToPosition(Duration.zero);
          };
        }

        return InkWell(
          onTap: onPressed,
          child: Container(
            padding: EdgeInsets.all(context.isTablet ? 8 : 4.w),
            decoration: BoxDecoration(
              color: widget.backgroundColor,
              borderRadius: BorderRadius.circular(100.r),
            ),
            child: SvgPicture.asset(
              iconAsset,
              width: context.isTablet ? 42 : 36.w,
              height: context.isTablet ? 42 : 36.w,
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlaybackSpeedButton() {
    return ValueListenableBuilder<double>(
      valueListenable: _playbackSpeed,
      builder: (context, speed, _) {
        return IconButton(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          padding: EdgeInsets.zero,
          icon: Text(
            '${speed}x',
            style: TextStyle(
              fontSize: context.isTablet ? 17 : 14.sp,
              fontWeight: FontWeight.w400,
              color: context.colorScheme.mainGray,
            ),
          ),
          onPressed: _changePlaybackSpeed,
        );
      },
    );
  }

  void _changePlaybackSpeed() {
    if (_audioPlayer == null) return;

    HapticFeedback.lightImpact();
    if (_audioPlayer!.playerState.processingState == ProcessingState.completed) {
      AudioPlayerManager.instance.seekToPosition(Duration.zero);
      AudioPlayerManager.instance.play();
    }

    _currentSpeedIndex = (_currentSpeedIndex + 1) % _playbackSpeeds.length;
    double newSpeed = _playbackSpeeds[_currentSpeedIndex];
    AudioPlayerManager.instance.setSpeed(newSpeed);

    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.audio_player_speed_change,
    );
  }

  Widget _buildProgressBar() {
    return ValueListenableBuilder<Duration>(
      valueListenable: _audioDuration,
      builder: (context, duration, child) {
        return ValueListenableBuilder<Duration>(
          valueListenable: _audioPosition,
          builder: (context, position, child) {
            final positionMillis = position.inMilliseconds
                .toDouble()
                .clamp(0.0, duration.inMilliseconds.toDouble());

            return SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: 4.0,
                thumbShape:
                    const RoundSliderThumbShape(enabledThumbRadius: 6.0),
                overlayShape:
                    const RoundSliderOverlayShape(overlayRadius: 10.0),
                activeTrackColor: context.colorScheme.mainBlue,
                inactiveTrackColor: context.colorScheme.mainGray,
                thumbColor: AppColors.white,
                overlayColor: Colors.blue.withOpacity(0.4),
                // Configure time bubble display
                showValueIndicator: ShowValueIndicator.onlyForDiscrete,
                valueIndicatorColor: context.colorScheme.mainNeutral,
                // valueIndicatorShape: const PaddleSliderValueIndicatorShape(),
                valueIndicatorTextStyle: TextStyle(
                  color: context.colorScheme.mainGray,
                  fontSize: context.isTablet ? 16 : 14.sp,
                  fontWeight: FontWeight.w400,
                ),
                rangeValueIndicatorShape:
                    const PaddleRangeSliderValueIndicatorShape(),
              ),
              child: Slider(
                min: 0.0,
                max: duration.inMilliseconds.toDouble(),
                value: positionMillis,
                // Add divisions to enable valueIndicator
                divisions: duration.inMilliseconds > 0
                    ? duration.inMilliseconds ~/ 1000
                    : 1,
                label:
                    " ${_formatDuration(position)} | ${_formatDuration(duration)} ",
                onChangeStart: (value) {
                  AudioPlayerManager.instance.setDragging(true);
                },
                onChanged: (value) {
                  _audioPosition.value = Duration(milliseconds: value.round());
                },
                onChangeEnd: (value) {
                  AudioPlayerManager.instance.setDragging(false);
                  AudioPlayerManager.instance.seekToPosition(Duration(milliseconds: value.round()));
                  AnalyticsService.logAnalyticsEventNoParam(
                    eventName: EventName.audio_player_seek,
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildTimeDisplay() {
    return StreamBuilder<Duration>(
      stream: _audioPlayer?.positionStream,
      builder: (context, snapshot) {
        final position = snapshot.data ?? Duration.zero;
        // final remainingDuration = _audioDuration.value - position;
        return Text(
          _formatDuration(position),
          style: TextStyle(
            fontSize: context.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w500,
            color: context.colorScheme.mainGray,
          ),
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return duration.inHours > 0
        ? '$hours:$minutes:$seconds'
        : '$minutes:$seconds';
  }
}
