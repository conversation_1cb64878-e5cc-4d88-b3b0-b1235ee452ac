import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../ui/pages/onboarding/cubit/onboarding_state.dart';

class OnBoardingPortraitPage extends StatefulWidget {
  const OnBoardingPortraitPage({
    Key? key,
  }) : super(key: key);

  @override
  OnBoardingPortraitPageState createState() => OnBoardingPortraitPageState();
}

class OnBoardingPortraitPageState
    extends BasePageStateDelegate<OnBoardingPortraitPage, OnboardingCubit>
    with TickerProviderStateMixin {
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // Preload all images
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AppsFlyerService.initATTAppsFlyersFacebookTiktok();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  List<OnboardingModel> get onboardingItems =>
      OnboardingItems.getItems(context);

  List<OnboardingModel> get onboardingProfessionItems =>
      OnboardingProfessionalItems.getProfessionalItems(context);

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<OnboardingCubit, OnboardingState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: context.colorScheme.mainBackground,
          body: Stack(
            children: [
              if (state.isGetStartedScreen) ...[
                Container(
                  decoration: context.isDarkMode
                      ? BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage(
                              Assets.images.bgrBlurOb1.path,
                            ),
                            fit: BoxFit.cover,
                          ),
                        )
                      : null,
                ),
                Lottie.asset(
                  Assets.videos.onb1,
                  height: double.infinity,
                  width: double.infinity,
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        Assets.icons.icAverageRating,
                        width: 166.w,
                        height: 65.h,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                      AppConstants.kSpacingItem20,
                      _buildContentIntroStarted(),
                      AppConstants.kSpacingItem41,
                      _buildGetStartedButton(),
                      AppConstants.kSpacingItem40,
                    ],
                  ),
                ),
              ] else if (state.isChooseTypeScreen) ...[
                _showChooseTypePage(state)
              ] else if (state.isCustomTabScreen) ...[
                const CustomTabPage()
              ] else ...[
                /// Type Student
                if (state.selectedTypeIndex == 0) ...[
                  PageView.builder(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: onboardingItems.length,
                    itemBuilder: (context, index) {
                      final items = onboardingItems[index];
                      return Stack(
                        children: [
                          Center(child: _buildBackgroundImages(index)),

                          /// blur
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: Container(
                              decoration: context.isDarkMode
                                  ? BoxDecoration(
                                      image: DecorationImage(
                                        image: AssetImage(
                                          Assets.images.bgBlurOb.path,
                                        ),
                                        fit: cubit.appCubit.isTablet
                                            ? BoxFit.fill
                                            : BoxFit.cover,
                                      ),
                                    )
                                  : null,
                              height: MediaQuery.of(context).size.height * 0.46,
                            ),
                          ),

                          /// Content of PageView
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: MediaQuery.of(context).size.height * 0.2,
                            child: context.isTablet
                                ? _buildContentIntroTablet(items)
                                : _buildContentIntro(items),
                          ),
                        ],
                      );
                    },
                    onPageChanged: (value) {
                      if (value < state.currentIndex) {
                        _pageController.animateToPage(
                          state.currentIndex,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                        return;
                      }
                      cubit.trackingEventTypeStudent(value - 1);
                      cubit.updateCurrentIndex(value);
                    },
                  ),

                  /// Dot Indicator
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Column(
                      children: [
                        Center(
                          child: SmoothPageIndicator(
                            controller: _pageController,
                            count: onboardingItems.length,
                            effect: JumpingDotEffect(
                              dotWidth: cubit.appCubit.isTablet ? 12 : 8,
                              dotHeight: cubit.appCubit.isTablet ? 12 : 8,
                              activeDotColor: context.colorScheme.mainBlue,
                              dotColor:
                                  context.colorScheme.mainGray.withOpacity(0.5),
                              spacing: cubit.appCubit.isTablet ? 12 : 8,
                              verticalOffset: 10,
                              jumpScale: 2.0,
                            ),
                          ),
                        ),
                        AppConstants.kSpacingItem32,
                        _buildContinueWidget(),
                        AppConstants.kSpacingItem40,
                      ],
                    ),
                  ),
                ] else ...[
                  /// Type Business
                  PageView.builder(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: onboardingProfessionItems.length,
                    itemBuilder: (context, index) {
                      final items = onboardingProfessionItems[index];
                      return Stack(
                        children: [
                          Center(
                            child: _buildBackgroundImagesProfessional(index),
                          ),

                          /// Content of PageView
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: MediaQuery.of(context).size.height * 0.2,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                context.isTablet
                                    ? _buildContentIntroTablet(items)
                                    : _buildContentIntro(items),
                              ],
                            ),
                          ),

                          /// Blur
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: Container(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(
                                    Assets.images.bgBlurOb.path,
                                  ),
                                  fit: cubit.appCubit.isTablet
                                      ? BoxFit.fill
                                      : BoxFit.cover,
                                ),
                              ),
                              height: MediaQuery.of(context).size.height * 0.46,
                            ),
                          ),
                        ],
                      );
                    },
                    onPageChanged: (value) {
                      if (value < state.currentIndex) {
                        _pageController.animateToPage(
                          state.currentIndex,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                        return;
                      }
                      cubit.trackingEventTypeProfessional(value - 1);
                      cubit.updateCurrentIndex(value);
                    },
                  ),

                  /// Dot Indicator
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Column(
                      children: [
                        Center(
                          child: SmoothPageIndicator(
                            controller: _pageController,
                            count: onboardingProfessionItems.length,
                            effect: JumpingDotEffect(
                              dotWidth: cubit.appCubit.isTablet ? 12 : 8,
                              dotHeight: cubit.appCubit.isTablet ? 12 : 8,
                              activeDotColor: context.colorScheme.mainBlue,
                              dotColor:
                                  context.colorScheme.mainGray.withOpacity(0.5),
                              spacing: cubit.appCubit.isTablet ? 16 : 12,
                              verticalOffset: 10,
                              jumpScale: 2.0,
                            ),
                          ),
                        ),
                        AppConstants.kSpacingItem32,
                        _buildContinueWidget(),
                        AppConstants.kSpacingItem40,
                      ],
                    ),
                  ),
                ],
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildContentIntro(OnboardingModel item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CommonText(
          item.content,
          variant: Variant.h4,
          appFontWeight: AppFontWeight.semiBold,
          letterSpacing: 0.84,
          maxLines: 2,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CommonText(
              item.subtitle,
              variant: Variant.h4,
              appFontWeight: AppFontWeight.semiBold,
              letterSpacing: 0.84,
            ),
            // AppConstants.kSpacingItemW4,
            // if (widget.icon != null) SvgPicture.asset(widget.icon ?? '')
          ],
        ),
        AppConstants.kSpacingItem8,
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CommonText(
              item.title,
              style: TextStyle(
                fontSize: 16.sp,
                color: context.colorScheme.mainGray,
                fontWeight: FontWeight.w400,
              ),
              letterSpacing: 0.32,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContentIntroTablet(OnboardingModel item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CommonText(
          item.content,
          style: const TextStyle(fontSize: 36),
          textColor: Colors.white,
          appFontWeight: AppFontWeight.semiBold,
        ),
        CommonText(
          item.subtitle,
          style: const TextStyle(fontSize: 36),
          textColor: Colors.white,
          appFontWeight: AppFontWeight.semiBold,
        ),
        AppConstants.kSpacingItem8,
        CommonText(
          item.title,
          style: const TextStyle(fontSize: 24),
          textColor: context.colorScheme.mainGray,
          appFontWeight: AppFontWeight.regular,
          letterSpacing: 0.48,
        ),
      ],
    );
  }

  /// Continue Button Widget
  Widget _buildContinueWidget() {
    return BlocBuilder<OnboardingCubit, OnboardingState>(
      builder: (context, state) {
        final isLastItem = state.selectedTypeIndex == 0
            ? state.currentIndex == onboardingItems.length - 1
            : state.currentIndex == onboardingProfessionItems.length - 1;

        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: cubit.appCubit.isTablet ? 180 : 48.w,
          ),
          child: AppCommonButton(
            width: cubit.appCubit.isTablet
                ? MediaQuery.of(context).size.height * 0.3
                : double.infinity,
            height: cubit.appCubit.isTablet ? 64 : 56.h,
            borderRadius: BorderRadius.circular(100.r),
            gradient: const LinearGradient(
              begin: Alignment.centerRight,
              end: Alignment.centerLeft,
              colors: AppColors.gradientCTABlue,
            ),
            textWidget: CommonText(
              S.current.continue_button,
              style: TextStyle(
                height: 1,
                fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                fontWeight: FontWeight.w600,
                color: context.colorScheme.mainPrimary,
              ),
            ),
            onPressed: () {
              if (isLastItem) {
                cubit.navigateToCustomTabScreen();
              } else {
                _pageController.nextPage(
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.linear,
                );
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildContentIntroStarted() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ShaderMask(
              shaderCallback: (bounds) => const LinearGradient(
                colors: AppColors.gradientCTABlue,
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ).createShader(bounds),
              child: CommonText(
                S.current.smart_note_big_ideas,
                style: TextStyle(
                  fontSize: context.isTablet ? 36 : 24.sp,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
        CommonText(
          S.current.let_note_ai,
          style: TextStyle(fontSize: context.isTablet ? 24 : 16.sp),
          textColor: context.colorScheme.mainGray,
          appFontWeight: AppFontWeight.regular,
        ),
        CommonText(
          S.current.chaos_into_clarity,
          style: TextStyle(fontSize: context.isTablet ? 24 : 16.sp),
          textColor: context.colorScheme.mainGray,
          appFontWeight: AppFontWeight.regular,
        ),
      ],
    );
  }

  Widget _buildGetStartedButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 48.w),
      child: AppCommonButton(
        width: cubit.appCubit.isTablet
            ? MediaQuery.of(context).size.height * 0.3
            : double.infinity,
        height: cubit.appCubit.isTablet ? 64 : 56.h,
        borderRadius: BorderRadius.circular(100.r),
        gradient: const LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: AppColors.gradientCTABlue,
        ),
        leftIcon: SvgPicture.asset(
          width: 24.w,
          height: 24.h,
          Assets.icons.icStarted,
          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
        ),
        textWidget: CommonText(
          height: 1,
          S.current.get_start,
          style: TextStyle(
            fontSize: cubit.appCubit.isTablet ? 20 : 20.sp,
            fontWeight: FontWeight.w600,
            color: context.colorScheme.mainPrimary,
          ),
        ),
        onPressed: () async {
          cubit.setGetStartedScreen(false);
          cubit.setChooseTypeScreen(true);
          AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.onboarding_get_started_next);
        },
      ),
    );
  }

  Widget _buildBackgroundImages(int currentIndex) {
    final item = onboardingItems[currentIndex];
    if (item.isShowJsonToBgr) {
      return Lottie.asset(
        item.jsonUrl,
        fit: BoxFit.cover,
      );
    } else {
      return BackgroundOnboardingVideoWidget(
        urlVideo: item.videoUrl,
      );
    }
  }

  Widget _buildBackgroundImagesProfessional(int currentIndex) {
    final item = onboardingProfessionItems[currentIndex];
    if (item.isShowJsonToBgr) {
      return Lottie.asset(item.jsonUrl, fit: BoxFit.cover);
    } else {
      return BackgroundOnboardingVideoWidget(
        urlVideo: item.videoUrl,
      );
    }
  }

  Widget _showChooseTypePage(OnboardingState state) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 83.h,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Center(
            child: CommonText(
              S.current.choose_your_note_experience,
              style: TextStyle(
                fontSize: context.isTablet ? 30 : 22.sp,
                fontWeight: FontWeight.w600,
                color: context.colorScheme.mainPrimary,
              ),
            ),
          ),
          Center(
            child: CommonText(
              S.current.select_your_primary_use_case,
              style: TextStyle(
                fontSize: context.isTablet ? 24 : 16.sp,
                fontWeight: FontWeight.w400,
                color: context.colorScheme.mainGray,
              ),
            ),
          ),
          AppConstants.kSpacingItem18,
          SvgPicture.asset(
            state.selectedTypeIndex == -1
                ? Assets.icons.icTempOb
                : state.selectedTypeIndex == 0
                    ? Assets.icons.typeStudent
                    : Assets.icons.typeBusiness,
            width: 255.w,
            height: 241.h,
          ),
          AppConstants.kSpacingItem50,
          ChooseTypeItem(
            icon: state.selectedTypeIndex == 0
                ? Assets.icons.icStudentSelect
                : Assets.icons.icStudentUnselect,
            title: S.current.student,
            subtitle: S.current.lecture_notes_study_materials,
            isSelected: state.selectedTypeIndex == 0,
            onSelected: (selected) {
              cubit.updateSelectedType(0);
            },
          ),
          AppConstants.kSpacingItem16,
          ChooseTypeItem(
            icon: state.selectedTypeIndex == 1
                ? Assets.icons.icProSelect
                : Assets.icons.icProUnselect,
            title: S.current.professional,
            subtitle: S.current.work_notes_projects,
            isSelected: state.selectedTypeIndex == 1,
            onSelected: (selected) {
              cubit.updateSelectedType(1);
            },
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 32.w),
            child: AppCommonButton(
              disabled: state.selectedTypeIndex == -1,
              width: cubit.appCubit.isTablet
                  ? MediaQuery.of(context).size.height * 0.3
                  : double.infinity,
              height: context.isTablet ? 64 : 56.h,
              borderRadius: BorderRadius.circular(100.r),
              gradient: LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: state.selectedTypeIndex == -1
                    ? [
                        context.colorScheme.mainNeutral,
                        context.colorScheme.mainNeutral,
                      ]
                    : AppColors.gradientCTABlue,
              ),
              textWidget: CommonText(
                S.current.continue_button,
                height: 1,
                style: TextStyle(
                  fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                  fontWeight: FontWeight.w600,
                  color: state.selectedTypeIndex == -1
                      ? context.colorScheme.mainGray
                      : context.colorScheme.mainPrimary,
                ),
              ),
              onPressed: () {
                cubit.setChooseTypeScreen(false);
                AnalyticsService.logAnalyticsEventNoParam(
                    eventName: EventName.onboarding_choose_type_next);
                AnalyticsService.logAnalyticsEventNoParam(
                    eventName: state.selectedTypeIndex == 0
                        ? EventName.onboarding_1_student
                        : EventName.onboarding_1_business);
              },
            ),
          ),
          AppConstants.kSpacingItem40,
        ],
      ),
    );
  }
}

Future<dynamic> buildModalAbout(
  BuildContext context,
  WebViewController controller,
) {
  return showModalBottomSheet(
    showDragHandle: true,
    enableDrag: false,
    //allows scrolling of the WebView.
    isScrollControlled: true,
    context: context,
    builder: (BuildContext context) {
      return FractionallySizedBox(
        heightFactor: 0.9,
        child: WebViewWidget(
          controller: controller,
        ),
      );
    },
  );
}
