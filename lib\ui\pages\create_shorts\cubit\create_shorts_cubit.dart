import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:video_player/video_player.dart';

import 'create_shorts_state.dart';

class CreateShortsCubit extends BaseCubit<CreateShortsState> {
  VideoPlayerController? videoController;
  final Map<String, VideoPlayerController> _preloadedControllers = {};

  CreateShortsCubit(super.initialState);

  @override
  Future<void> close() {
    videoController?.dispose();
    _disposePreloadedControllers();
    return super.close();
  }

  void _disposePreloadedControllers() {
    for (var controller in _preloadedControllers.values) {
      controller.dispose();
    }
    _preloadedControllers.clear();
  }

  int _selectedDurationIndex = 0;

  int get selectedDurationIndex => _selectedDurationIndex;

  final _backgroundVideoApiService =
      GetIt.instance.get<BackgroundVideoApiServiceImpl>().getAllBackgroundVideo;

  final _backgroundQuizVideoApiService = GetIt.instance
      .get<BackgroundVideoApiServiceImpl>()
      .getAllBackgroundQuizVideo;

  late final List<BackgroundVideoDto> _listBackgroundVideo;

  late final List<BackgroundQuizVideoDto> _listBackgroundQuizVideo;

  List<BackgroundVideoDto> get backgroundVideos => _listBackgroundVideo;

  List<BackgroundQuizVideoDto> get backgroundQuizVideos =>
      _listBackgroundQuizVideo;

  void updateSelectedDurationIndex(int index) {
    _selectedDurationIndex = index;
    emit(state.copyWith(selectedDurationIndex: index));
  }

  void updateSelectedIndex(int index, bool isQuizVideo) async {
    // Pause and dispose current video if any
    videoController?.pause();
    videoController?.dispose();
    videoController = null;

    // Update the appropriate selected index based on type
    if (isQuizVideo) {
      if (state.selectedQuizVideoIndex == index) return;
      emit(state.copyWith(
        selectedQuizVideoIndex: index,
        selectedVideoIndex: -1, // Reset other type's selection
        isPlaying: false,
        isLoading: true,
      ));
    } else {
      if (state.selectedVideoIndex == index) return;
      emit(state.copyWith(
        selectedVideoIndex: index,
        selectedQuizVideoIndex: -1, // Reset other type's selection
        isPlaying: false,
        isLoading: true,
      ));
    }

    // Get the selected video URL based on type
    String videoUrl;
    if (isQuizVideo) {
      // Handle BackgroundQuizVideoDto
      final selectedQuizVideo = state.listBackgroundQuizVideos[index];
      videoUrl = selectedQuizVideo.videoUrl;
    } else {
      // Handle BackgroundVideoDto
      final selectedVideo = state.listBackgroundVideos[index];
      videoUrl = selectedVideo.videoUrl;
    }

    // Check if we have a preloaded controller
    if (_preloadedControllers.containsKey(videoUrl)) {
      videoController = _preloadedControllers[videoUrl];
      _preloadedControllers.remove(videoUrl);

      // Start playing immediately since it's already initialized
      await videoController!.play();
      emit(state.copyWith(
        isPlaying: true,
        isLoading: false,
      ));
    } else {
      // Initialize new controller if not preloaded
      await initializeVideo(videoUrl);
    }

    // Preload next video if available
    _preloadNextVideo(index, isQuizVideo);
  }

  Future<void> _preloadNextVideo(int currentIndex, bool isQuizVideo) async {
    String? nextVideoUrl;
    if (isQuizVideo) {
      if (currentIndex + 1 >= state.listBackgroundQuizVideos.length) return;
      nextVideoUrl = state.listBackgroundQuizVideos[currentIndex + 1].videoUrl;
    } else {
      if (currentIndex + 1 >= state.listBackgroundVideos.length) return;
      nextVideoUrl = state.listBackgroundVideos[currentIndex + 1].videoUrl;
    }

    if (!_preloadedControllers.containsKey(nextVideoUrl)) {
      try {
        final controller =
            VideoPlayerController.networkUrl(Uri.parse(nextVideoUrl));
        await controller.initialize();
        _preloadedControllers[nextVideoUrl] = controller;
      } catch (e) {
        debugPrint('Error preloading video: $e');
      }
    }
  }

  void getAllBackgroundVideos() async {
    emit(state.copyWith(oneShotEvent: BackgroundVideoOneShotEvent.loading));
    try {
      final data = await _backgroundVideoApiService();
      if (data.isEmpty) {
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.backGroundVideoIsEmpty,
        );
      }
      _listBackgroundVideo = data;
      for (var bgDto in data) {
        GetIt.instance.get<CacheService>().cacheFile(bgDto.videoUrl);
      }

      // Preload first video
      if (data.isNotEmpty) {
        _preloadNextVideo(-1, false);
      }

      emit(
        state.copyWith(
          listBackgroundVideos: data,
          oneShotEvent: BackgroundVideoOneShotEvent.success,
        ),
      );
    } catch (err) {
      emit(state.copyWith(oneShotEvent: BackgroundVideoOneShotEvent.error));
    }
  }

  void getAllBackgroundQuizVideos() async {
    emit(state.copyWith(
      quizOneShotEvent: BackgroundQuizVideoOneShotEvent.loading,
    ));
    try {
      final data = await _backgroundQuizVideoApiService();
      if (data.isEmpty) {
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.backGroundVideoIsEmpty,
        );
      }
      _listBackgroundQuizVideo = data;
      for (var bgDto in data) {
        GetIt.instance.get<CacheService>().cacheFile(bgDto.videoUrl);
      }

      // Preload first video
      if (data.isNotEmpty) {
        _preloadNextVideo(-1, true);
      }

      emit(
        state.copyWith(
          listBackgroundQuizVideos: data,
          quizOneShotEvent: BackgroundQuizVideoOneShotEvent.success,
        ),
      );
    } catch (err) {
      emit(state.copyWith(
        quizOneShotEvent: BackgroundQuizVideoOneShotEvent.error,
      ));
    }
  }

  List<dynamic> getCombinedList() {
    return [...state.listBackgroundQuizVideos, ...state.listBackgroundVideos];
  }

  BackgroundVideoDto? getCurrentSelectedItem() {
    final combinedList = getCombinedList();
    if (combinedList.isNotEmpty) {
      if (state.selectedQuizVideoIndex != -1) {
        final selectedItem =
            state.listBackgroundQuizVideos[state.selectedQuizVideoIndex];
        return BackgroundVideoDto(
          backgroundId: selectedItem.templateId,
          title: selectedItem.title,
          width: selectedItem.width,
          height: selectedItem.height,
          thumbnailUrl: selectedItem.thumbnailUrl,
          videoUrl: selectedItem.videoUrl,
        );
      } else if (state.selectedVideoIndex != -1) {
        return state.listBackgroundVideos[state.selectedVideoIndex];
      }
    }
    return null;
  }

  bool isSelectedVideoQuiz() {
    return state.selectedQuizVideoIndex != -1;
  }

  void toggleValueVideoCaption(bool isCaptionEnabled) {
    emit(state.copyWith(isCaptionEnabled: isCaptionEnabled));
  }

  void onShortsBack(CreateShortsType type) =>
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: type == CreateShortsType.audio
            ? EventName.podcast_btmsht_back
            : EventName.shorts_back,
      );

  Future<void> initializeVideo(String videoUrl) async {
    try {
      emit(state.copyWith(isLoading: true));
      videoController = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
      await videoController!.initialize();
      videoController!.setLooping(true);

      await videoController!.play();
      emit(state.copyWith(
        error: null,
        isPlaying: true,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
        isLoading: false,
      ));
    }
  }

  Future<void> togglePlayPause() async {
    if (videoController == null || !videoController!.value.isInitialized) {
      return;
    }

    if (videoController!.value.isPlaying) {
      await videoController!.pause();
      emit(state.copyWith(isPlaying: false));
    } else {
      await videoController!.play();
      emit(state.copyWith(isPlaying: true));
    }
  }
}
