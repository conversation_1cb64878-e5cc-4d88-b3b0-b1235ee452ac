import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/base/app/app_state.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/login/account_page_state.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'package:intl/intl.dart';

class AccountPage extends StatefulWidget {
  final PackageProductDto? packageProductDto;

  const AccountPage({super.key, this.packageProductDto});

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState
    extends BasePageStateDelegate<AccountPage, AccountPageCubit> {
  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<AccountPageCubit, AccountPageState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent &&
          current.oneShotEvent != AccountOneShotEvent.none,
      listener: (context, state) {
        if (state.oneShotEvent == AccountOneShotEvent.logoutOrDeleteSuccess) {
          cubit.appCubit.setCurrentScreen(AppScreen.loginFromSplash);
        }
        cubit.resetEnumState();
      },
      child: child,
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colorScheme.mainBackground,
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          color: context.colorScheme.mainBackground,
          child: _buildContent(),
        ),
      ),
    );
  }

  AppBarWidget _buildAppBar(BuildContext context) {
    return AppBarWidget(
      customTitle: Text(
        S.current.account,
        style: TextStyle(
          fontSize: context.isTablet ? 18 : 16.sp,
          fontWeight: FontWeight.w600,
          color: context.colorScheme.mainPrimary,
        ),
      ),
      backgroundColor: context.colorScheme.mainBackground,
      onPressed: () {
        Navigator.pop(context);
        cubit.onAccountBack();
      },
      actions: [_buildMoreButton(context)],
    );
  }

  Widget _buildMoreButton(BuildContext context) {
    return PullDownButton(
      routeTheme: PullDownMenuRouteTheme(
        backgroundColor: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(16.r),
        width: context.isTablet ? 200 : 200.w,
      ),
      buttonBuilder: (context, showMenu) => IconButton(
        onPressed: () {
          showMenu();
          cubit.onAccountMorePressed();
        },
        icon: SvgPicture.asset(
          Assets.icons.icMore,
          width: context.isTablet ? 24 : 24.w,
          height: context.isTablet ? 24 : 24.w,
          colorFilter: ColorFilter.mode(
            context.colorScheme.mainPrimary,
            BlendMode.srcIn,
          ),
        ),
      ),
      itemBuilder: (context) => [
        PullDownMenuItem(
          onTap: () {
            cubit.onAccountLogOut();
            _handleLogout();
          },
          title: S.current.logout,
          iconWidget: SvgPicture.asset(
            cubit.appCubit.isReverseView
                ? Assets.icons.icFlipLogOut
                : Assets.icons.icLogout,
            width: context.isTablet ? 24 : 24.w,
            height: context.isTablet ? 24 : 24.w,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
        ),
        PullDownMenuItem(
          onTap: () {
            cubit.onAccountDeleteAccount();
            _handleDeleteAccount();
          },
          iconWidget: SvgPicture.asset(
            cubit.appCubit.isReverseView
                ? Assets.icons.icFlipDeleteAccount
                : Assets.icons.icDeleteAccount,
            width: context.isTablet ? 24 : 24.w,
            height: context.isTablet ? 24 : 24.w,
            colorFilter: const ColorFilter.mode(
              AppColors.primaryRed,
              BlendMode.srcIn,
            ),
          ),
          title: S.current.delete_account,
          isDestructive: true,
        ),
      ],
    );
  }

  Widget _buildContent() {
    return FutureBuilder<bool>(
      future: cubit.shouldShowSettingInfoSection(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: context.colorScheme.mainBlue,
            ),
          );
        }

        return SizedBox(
          height: AppConstants.appHeight,
          child: snapshot.data == true
              ? SettingInfoSection(
                  userType: cubit.appCubit.getAppUser().userType,
                )
              : Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: _buildSubscriptionCards(),
                      ),
                    ),
                    // Button positioned at the bottom
                    _buildButtonChangePlan(),
                    AppConstants.kSpacingItem8,
                    CommonText(
                      S.current.subscribe_via_web,
                      style: TextStyle(
                        fontSize: cubit.appCubit.isTablet ? 12 : 10.sp,
                        fontWeight: FontWeight.w400,
                        color: context.colorScheme.mainGray,
                      ),
                    ),
                    SizedBox(height: 24.h),
                  ],
                ),
        );
      },
    );
  }

  void _handleDeleteAccount() {
    showNewCupertinoDialog(
        context: context,
        title: S.current.delete_account,
        message: S.current.delete_account_detail,
        image: Assets.icons.icDiscardChanges,
        cancelButton: S.current.cancel,
        confirmButton: S.current.delete,
        onCancel: () {
          AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.account_delete_account_cancel,
          );
        },
        onConfirm: () {
          AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.account_delete_account_confirm,
          );
          cubit.handleDeleteAccount();
        });
  }

  void _handleLogout() {
    showNewCupertinoDialog(
        context: context,
        title: S.current.logout_question_mark,
        image: Assets.icons.icMascotLogout,
        cancelButton: S.current.cancel,
        confirmButton: S.current.logout,
        message: S.current.logout_detail,
        onCancel: () {
          AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.account_log_out_cancel,
          );
        },
        onConfirm: () {
          AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.account_log_out_confirm,
          );
          cubit.handleLogout();
        });
  }

  Widget _buildSubscriptionCards() {
    return BlocBuilder<AccountPageCubit, AccountPageState>(
      buildWhen: (previous, current) =>
          previous.selectedPlan != current.selectedPlan ||
          previous.subscriptionInfo != current.subscriptionInfo,
      builder: (context, state) {
        debugPrint('duonghaha state: $state');
        if (state.isLoading) {
          return _buildLoadingIndicator();
        }

        final formattedDates = _getFormattedDates(state.subscriptionInfo);

        return SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current Plan Section
              _buildPlanCard(
                  state.currentPlan,
                  state,
                  true,
                  formattedDates.formattedExpiryDate,
                  formattedDates.timeRemaining),

              // Available Plans Section
              ...state.availableUpdatePlans
                  .map((plan) => _buildPlanCard(
                      plan,
                      state,
                      false,
                      formattedDates.formattedExpiryDate,
                      formattedDates.timeRemaining))
                  .toList(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.5,
      child: Center(
        child: CupertinoActivityIndicator(radius: 16.r),
      ),
    );
  }

  ({String formattedExpiryDate, String timeRemaining}) _getFormattedDates(
      Map<String, DateTime>? subscriptionInfo) {
    String formattedExpiryDate = "";
    String timeRemaining = "";

    if (subscriptionInfo != null &&
        subscriptionInfo['expirationDate'] != null) {
      final expirationDate = subscriptionInfo['expirationDate']?.toUtc();
      final dateFormatter = DateFormat('dd/MM/yyyy');
      formattedExpiryDate = dateFormatter.format(expirationDate!);

      final currentDate = DateTime.now().toUtc();
      final difference = expirationDate.difference(currentDate);
      timeRemaining = '${difference.inDays}';
    }

    return (
      formattedExpiryDate: formattedExpiryDate,
      timeRemaining: timeRemaining
    );
  }

  Widget _buildPlanCard(UpgradeSubscriptionPlan plan, AccountPageState state,
      bool isCurrentPlan, String formattedExpiryDate, String timeRemaining) {
    bool isTrial = cubit.activePurchasePlan?.isTrial == true;
    String rawPrice = _getRawPrice(plan);

    return _getPlanCardWidget(
        plan: plan,
        state: state,
        isCurrentPlan: isCurrentPlan,
        isTrial: isTrial,
        rawPrice: rawPrice,
        formattedExpiryDate: formattedExpiryDate,
        timeRemaining: timeRemaining);
  }

  String _getRawPrice(UpgradeSubscriptionPlan plan) {
    final packages = cubit.appCubit.getRCPackages();
    final planIndex = _getPlanIndex(plan);

    if (planIndex >= 0 && planIndex < packages.length) {
      return packages[planIndex].storeProduct.priceString;
    }
    return '';
  }

  Widget _getPlanCardWidget(
      {required UpgradeSubscriptionPlan plan,
      required AccountPageState state,
      required bool isCurrentPlan,
      required bool isTrial,
      required String rawPrice,
      required String formattedExpiryDate,
      required String timeRemaining}) {
    switch (plan) {
      case UpgradeSubscriptionPlan.none:
        return SubscriptionExpandableCard(
          title: S.current.account_basic,
          price: S.current.free,
          subtitle: S.current.account_content_basic,
          isSelected: state.selectedPlan == plan,
          onTap: () => cubit.selectSubscriptionPlan(plan),
          cardColor: context.colorScheme.mainGray.withOpacity(0.6),
          planType: "free",
          isCurrentPlan: isCurrentPlan,
        );

      case UpgradeSubscriptionPlan.weekly:
        return SubscriptionExpandableCard(
          title: S.current.weekly,
          price: _getFormattedPrice(plan),
          subtitle: _getSubscriptionSubtitle(isCurrentPlan, isTrial, rawPrice,
              formattedExpiryDate, timeRemaining),
          isSelected: state.selectedPlan == plan,
          onTap: () => cubit.selectSubscriptionPlan(plan),
          cardColor: AppColors.primaryBlue,
          planType: "weekly",
          isCurrentPlan: isCurrentPlan,
          isTrial: isTrial,
        );

      case UpgradeSubscriptionPlan.monthly:
        return SubscriptionExpandableCard(
          title: S.current.monthly,
          price: _getFormattedPrice(plan),
          subtitle: _getSubscriptionSubtitle(isCurrentPlan, isTrial, rawPrice,
              formattedExpiryDate, timeRemaining),
          isSelected: state.selectedPlan == plan,
          onTap: () => cubit.selectSubscriptionPlan(plan),
          cardColor: AppColors.primaryViolet,
          planType: "monthly",
          isCurrentPlan: isCurrentPlan,
          isTrial: isTrial,
        );

      case UpgradeSubscriptionPlan.quarterly:
        return SubscriptionExpandableCard(
          title: S.current.quarterly,
          price: _getFormattedPrice(plan),
          subtitle: isCurrentPlan
              ? S.current.next_bill_date(rawPrice, formattedExpiryDate)
              : S.current.account_content_pro,
          isSelected: state.selectedPlan == plan,
          onTap: () => cubit.selectSubscriptionPlan(plan),
          cardColor: AppColors.primaryBlue,
          planType: "quarterly",
          isCurrentPlan: isCurrentPlan,
        );

      case UpgradeSubscriptionPlan.yearly:
        return SubscriptionExpandableCard(
          title: S.current.yearly,
          price: _getFormattedPrice(plan),
          originalPrice: isCurrentPlan ? null : _getOriginalPrice(plan),
          subtitle: _getSubscriptionSubtitle(isCurrentPlan, isTrial, rawPrice,
              formattedExpiryDate, timeRemaining),
          isSelected: state.selectedPlan == plan,
          onTap: () => cubit.selectSubscriptionPlan(plan),
          cardColor: AppColors.primaryYellow,
          saveText: S.current.save_50,
          planType: "yearly",
          isCurrentPlan: isCurrentPlan,
          isTrial: isTrial,
        );

      case UpgradeSubscriptionPlan.essential:
        return SubscriptionExpandableCard(
          title: S.current.essential,
          features: [
            S.current.unlimited_youtube_document_ai_notes,
            S.current.audio_recording_ai_notes_daily,
            S.current.upgrade_to_pro_tier_at_a_special_price,
          ],
          isSelected: state.selectedPlan == plan,
          onTap: () => cubit.selectSubscriptionPlan(plan),
          gradient: const LinearGradient(
              colors: AppColors.gradientCTABluePink,
              begin: Alignment.centerLeft,
              end: Alignment.centerRight),
          planType: "essential",
          subtitle: null,
          isCurrentPlan: isCurrentPlan,
        );

      case UpgradeSubscriptionPlan.lifetime:
        return SubscriptionExpandableCard(
          title: S.current.account_lifetime,
          features: [
            S.current.unlock_all_features,
            S.current.free_updates,
          ],
          isSelected: state.selectedPlan == plan,
          onTap: () => cubit.selectSubscriptionPlan(plan),
          gradient: const LinearGradient(
              colors: AppColors.gradientCTAPinkBlue,
              begin: Alignment.centerLeft,
              end: Alignment.centerRight),
          planType: "lifetime",
          subtitle: null,
          isCurrentPlan: isCurrentPlan,
        );

      default:
        return const SizedBox.shrink();
    }
  }

  String _getSubscriptionSubtitle(bool isCurrentPlan, bool isTrial,
      String rawPrice, String formattedExpiryDate, String timeRemaining) {
    if (isTrial && isCurrentPlan) {
      return S.current.content_account_trial(timeRemaining);
    } else if (isCurrentPlan) {
      return S.current.next_bill_date(rawPrice, formattedExpiryDate);
    } else {
      return S.current.account_content_pro;
    }
  }

  String _getOriginalPrice(UpgradeSubscriptionPlan plan) {
    final packages = cubit.appCubit.getRCPackages();
    final planIndex = _getPlanIndex(plan);

    if (planIndex >= 0 && planIndex < packages.length) {
      final package = packages[planIndex];
      final price = double.parse(package.storeProduct.price.toString()) * 2;
      return '${package.storeProduct.priceString.replaceAll(RegExp(r'[0-9.,]+'), '')}${price.toStringAsFixed(2)}';
    }
    return '';
  }

  Widget _buildButtonChangePlan() {
    return BlocBuilder<AccountPageCubit, AccountPageState>(
      buildWhen: (previous, current) =>
          previous.selectedPlan != current.selectedPlan ||
          previous.currentPlan != current.currentPlan,
      builder: (context, state) {
        final isEnabled = state.selectedPlan != UpgradeSubscriptionPlan.none &&
            state.selectedPlan != state.currentPlan;
        final backgroundColor = isEnabled
            ? context.colorScheme.mainBlue
            : context.colorScheme.mainNeutral;

        return AppCommonButton(
          width: double.infinity,
          height: cubit.appCubit.isTablet ? 44 : 44.h,
          borderRadius: BorderRadius.circular(24.r),
          backgroundColor: backgroundColor,
          textWidget: Text(
            S.current.change_plan,
            style: TextStyle(
              fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
              fontWeight: FontWeight.w500,
              color: isEnabled
                  ? (Colors.white)
                  : context.colorScheme.mainPrimary.withOpacity(0.38),
            ),
          ),
          onPressed: () {
            if (isEnabled) {
              HapticFeedback.mediumImpact();
              cubit.updateSubscription();
              AnalyticsService.logAnalyticsEventNoParam(
                  eventName: EventName.account_change_plan);
            }
          },
        );
      },
    );
  }

  String _getFormattedPrice(UpgradeSubscriptionPlan plan) {
    final state = cubit.state;

    if ((plan == state.currentPlan || plan == state.selectedPlan) &&
        state.subscriptionPrices.containsKey(plan)) {
      final price = state.subscriptionPrices[plan]!;
      final packages = cubit.appCubit.getRCPackages();
      final planIndex = _getPlanIndex(plan);

      if (planIndex >= 0 && planIndex < packages.length) {
        final package = packages[planIndex];
        final currencyCode = package.storeProduct.currencyCode;
        return _formatPriceWithCurrency(price, currencyCode, plan);
      }
      return _formatPriceWithCurrency(price, '', plan);
    }

    final packages = cubit.appCubit.getRCPackages();
    final planIndex = _getPlanIndex(plan);

    if (planIndex == -1 || planIndex >= packages.length) {
      return plan == UpgradeSubscriptionPlan.none ? S.current.free : '';
    }

    final package = packages[planIndex];
    if (package.storeProduct.priceString.isEmpty) {
      return plan == UpgradeSubscriptionPlan.none ? S.current.free : '';
    }

    try {
      final price = double.parse(package.storeProduct.price.toString());
      final currencyCode = package.storeProduct.currencyCode;
      final formattedPrice = _formatPrice(price, currencyCode);

      return _formatCurrencyString(
          formattedPrice, currencyCode, package.storeProduct.priceString);
    } catch (e) {
      return package.storeProduct.priceString;
    }
  }

  String _formatCurrencyString(
      String formattedPrice, String currencyCode, String priceString) {
    switch (currencyCode) {
      case 'VND':
        return '$formattedPrice₫';
      case 'JPY':
        return '$formattedPrice¥';
      case 'KRW':
        return '$formattedPrice₩';
      case 'THB':
      case 'PHP':
        return '$currencyCode $formattedPrice';
      default:
        final currencyIcon = priceString.replaceAll(RegExp(r'[0-9.,\s]+'), '');
        final isCurrencyIconFirst = priceString.indexOf(currencyIcon) == 0;
        return isCurrencyIconFirst
            ? '$currencyIcon$formattedPrice'
            : '$formattedPrice$currencyIcon';
    }
  }

  int _getPlanIndex(UpgradeSubscriptionPlan plan) {
    switch (plan) {
      case UpgradeSubscriptionPlan.weekly:
        return 0;
      case UpgradeSubscriptionPlan.monthly:
      case UpgradeSubscriptionPlan.quarterly:
        return 1;
      case UpgradeSubscriptionPlan.yearly:
        return 2;
      case UpgradeSubscriptionPlan.lifetime:
        return 3;
      default:
        return -1;
    }
  }

  String _formatPrice(double price, String currencyCode) {
    final hasDecimalPart = price != price.roundToDouble();

    switch (currencyCode) {
      case 'VND':
        return NumberFormat('###,###', 'vi_VN').format(price.round());
      default:
        if (hasDecimalPart) {
          return NumberFormat('#,##0.00', 'en_US').format(price);
        } else {
          return NumberFormat('#,###', 'en_US').format(price.round());
        }
    }
  }

  String _formatPriceWithCurrency(
      double price, String currencyCode, UpgradeSubscriptionPlan plan) {
    final formattedPrice = _formatPrice(price, currencyCode);

    switch (currencyCode) {
      case 'VND':
        return '$formattedPrice₫';
      case 'JPY':
        return '$formattedPrice¥';
      case 'KRW':
        return '$formattedPrice₩';
      case 'THB':
      case 'PHP':
        return '$currencyCode $formattedPrice';
      default:
        return '\$$formattedPrice';
    }
  }
}
