import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_quill/translations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

class Application extends StatelessWidget {
  const Application({super.key});

  Locale getInitialLocale() {
    final deviceLocale = PlatformDispatcher.instance.locale;
    final supportedLocales = S.delegate.supportedLocales;

    if (supportedLocales.contains(deviceLocale)) {
      return deviceLocale;
    }

    final languageLocale = supportedLocales.firstWhere(
      (locale) => locale.languageCode == deviceLocale.languageCode,
      orElse: () => const Locale('en'), // Default to English
    );

    return languageLocale;
  }

  @override
  Widget build(BuildContext context) {
    final themeNotifier = GetIt.instance.get<ThemeNotifier>();
    return ValueListenableBuilder(
      valueListenable: themeNotifier,
      builder: (context, themeMode, child) {
        return ScreenUtilInit(
          designSize: const Size(375, 812),
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return MaterialApp(
              locale: getInitialLocale(),
              navigatorKey: g<NavigationService>().navigatorKey,
              debugShowCheckedModeBanner: false,
              themeMode: themeNotifier.getEffectiveTheme(),
              theme: lightTheme,
              darkTheme: darkTheme,
              supportedLocales: S.delegate.supportedLocales,
              localizationsDelegates: const [
                S.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
                FlutterQuillLocalizations.delegate,
              ],
              home: const AppPage(),
            );
          },
        );
      },
    );
  }
}
